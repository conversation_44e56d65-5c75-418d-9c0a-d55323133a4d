{"name": "cors-frontend", "version": "1.0.0", "license": "Commercial", "private": true, "engines": {"node": "22.x"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "build:icons": "tsx src/assets/iconify-icons/bundle-icons-css.ts", "postinstall": "npm run build:icons"}, "dependencies": {"@casl/ability": "^6.7.3", "@emotion/cache": "11.14.0", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.0", "@floating-ui/react": "0.27.2", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^7.0.2", "@mui/lab": "6.0.0-beta.19", "@mui/material": "6.2.1", "@mui/material-nextjs": "6.2.1", "@mui/utils": "^7.0.2", "@mui/x-date-pickers": "^8.4.0", "@reduxjs/toolkit": "^2.7.0", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-table": "^8.21.3", "@tanstack/table-core": "^8.21.3", "ExpandMore": "link:@mui/icons-material/ExpandMore", "FilterAlt": "link:@mui/icons-material/FilterAlt", "FilterList": "link:@mui/icons-material/FilterList", "axios": "^1.8.4", "classnames": "2.5.1", "date-fns": "^4.1.0", "formik": "^2.4.6", "jwt-decode": "^4.0.0", "next": "15.1.2", "next-auth": "^4.24.11", "react": "18.3.1", "react-colorful": "5.6.1", "react-dom": "18.3.1", "react-hook-form": "^7.56.0", "react-perfect-scrollbar": "1.5.8", "react-redux": "^9.2.0", "react-toastify": "^11.0.5", "react-use": "17.6.0", "redux-persist": "^6.0.0", "server-only": "0.0.1", "valibot": "^1.0.0", "yup": "^1.6.1"}, "devDependencies": {"@iconify/json": "2.2.286", "@iconify/tools": "4.1.1", "@iconify/types": "2.0.0", "@iconify/utils": "2.2.1", "@types/jwt-decode": "^3.1.0", "@types/next-auth": "^3.15.0", "@types/node": "^22.10.2", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-redux": "^7.1.34", "@types/yup": "^0.32.0", "@typescript-eslint/eslint-plugin": "7.18.0", "@typescript-eslint/parser": "7.18.0", "autoprefixer": "10.4.20", "eslint": "8.57.1", "eslint-config-next": "15.1.2", "eslint-config-prettier": "9.1.0", "eslint-import-resolver-typescript": "3.7.0", "eslint-plugin-import": "2.31.0", "postcss": "8.4.49", "postcss-styled-syntax": "0.7.0", "prettier": "3.4.2", "stylelint": "16.12.0", "stylelint-use-logical-spec": "5.0.1", "stylis": "4.3.4", "stylis-plugin-rtl": "2.1.1", "tailwindcss": "3.4.17", "tailwindcss-logical": "3.0.1", "tsx": "4.19.2", "typescript": "5.5.4"}, "resolutions": {"rimraf": "^5.0.7"}, "overrides": {"rimraf": "^5.0.7"}}