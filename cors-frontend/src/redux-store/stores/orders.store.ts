import { fetchOrders, filterOrder, FiltersType } from '@/actions/orders';
import apiClient from '@/utils/axios';
import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { createAction } from '@reduxjs/toolkit';

export interface BaseQueryParams {
  page?: number;
  limit?: number;
  q?: string;
  fq?: string;
}

export interface UpdateProductsParams {
  id: string;
  value: any;
}

type FilterCondition = {
    attribute: string;
    operator: string;
    value: string | number | boolean | (string | number | boolean)[];
  };
  
type FilterGroup = FilterCondition[];
  
type Filters = FilterGroup[];

type OrderData = {
    count : number,
    data : any[]
}
type OrderType = {
    flagged : OrderData,
    unflagged : OrderData
}

interface OrdersType {
    orders : OrderType,
    loading : boolean,
    error : string | null,
    filters : Filters
}

interface FetchOrdersPayload extends BaseQueryParams{
    isFlagged : boolean
}

const initialState: OrdersType = {
  orders: {
    flagged : {
        data : [],
        count : 0
    },
    unflagged : {
        data : [],
        count : 0
    }
  },
  filters: [],
  loading: false,
  error: null,
};


export const fetchOrdersAction = createAsyncThunk(
  "orders",
  async ({ page = 0, limit = 25, q, fq, isFlagged=false }: FetchOrdersPayload, { rejectWithValue }) => {
    try {
      const data = await fetchOrders({page, limit, q, fq : `flagged:eq:${isFlagged}`});
      
      // Validate the data structure
      if (typeof data === 'string' || !data) {
        console.error('Invalid data received:', data);
        return {[isFlagged ? 'flagged' : 'unflagged']: { data: [], count: 0 }};
      }
      
      return {[isFlagged ? 'flagged' : 'unflagged']: data};
    } catch (err: any) {
      console.error('Error in fetchOrdersAction:', err);
      // Return empty data instead of rejecting
      return {[isFlagged ? 'flagged' : 'unflagged']: { data: [], count: 0 }};
    }
  }
);

export const fetchAllOrdersAction = createAsyncThunk(
  "orders/fetchAll",
  async ({ page = 0, limit = 25, q, fq }: BaseQueryParams, { rejectWithValue }) => {
    try {
      const unflaggedPromise = fetchOrders({page, limit, q, fq: `flagged:eq:false`});
      const flaggedPromise = fetchOrders({page, limit, q, fq: `flagged:eq:true`});
      
      const [unflagged, flagged] = await Promise.all([unflaggedPromise, flaggedPromise]);
      
      return { unflagged, flagged };
    } catch (err: any) {
      return rejectWithValue(err.response?.data?.message || "Fetch failed");
    }
  }
);

// Add a new action to handle filter results
export const filterOrdersAction = createAsyncThunk(
  "orders/filter",
  async (filterParams: FiltersType, { rejectWithValue }) => {
    try {
      const data = await filterOrder(filterParams);
      
      const isFlagged = filterParams.filters.some(group => 
        group.some(filter => 
          filter.attribute === 'flagged' && filter.value === true
        )
      );
      
      return {[isFlagged ? 'flagged' : 'unflagged']: data};
    } catch (err: any) {
      console.error('Error in filterOrdersAction:', err);
      return rejectWithValue(err.response?.data?.message || "Filter failed");
    }
  }
);

const ordersSlice = createSlice({
  name: 'Orders',
  initialState,
  reducers: {
    setOrders: (state, action: PayloadAction<any>) => {
      state.orders = { ...state.orders, ...action.payload };
      
      if (action.payload.unflagged) {
        state.orders.unflagged = { 
          data: [...(action.payload.unflagged.data || [])], 
          count: action.payload.unflagged.count || 0
        };
      }
      if (action.payload.flagged) {
        state.orders.flagged = { 
          data: [...(action.payload.flagged.data || [])], 
          count: action.payload.flagged.count || 0
        };
      }
      
    },
    setOrdersFilters: (state, action: PayloadAction<Filters>) => {
      state.filters = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchOrdersAction.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchOrdersAction.fulfilled, (state, action) => {
        state.loading = false;
        state.orders = { ...state.orders,...action.payload};
      })
      .addCase(fetchOrdersAction.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch products';
      });
    builder
      .addCase(fetchAllOrdersAction.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllOrdersAction.fulfilled, (state, action) => {
        state.loading = false;
        state.orders = action.payload;
      })
      .addCase(fetchAllOrdersAction.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch orders';
      });
    builder
      .addCase(filterOrdersAction.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(filterOrdersAction.fulfilled, (state, action) => {
        state.loading = false;
        state.orders = { ...state.orders, ...action.payload };
      })
      .addCase(filterOrdersAction.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to filter orders';
      });
  
  },
});

export const {setOrders, setOrdersFilters} = ordersSlice.actions;
export default ordersSlice.reducer;

