import apiClient from '@/utils/axios';
import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { BaseQueryParams } from './orders.store';

function formatComplexFields(data: any[]): any[] {
  return data.map(item => {
    const formattedItem = { ...item };
    
    if (formattedItem.shopifyNativeVariant) {
      if (Array.isArray(formattedItem.shopifyNativeVariant)) {
        formattedItem.shopifyNativeVariantDisplay = formattedItem.shopifyNativeVariant
          .map((variant: any) => {
            if (typeof variant === 'object' && variant !== null) {
              return Object.values(variant).join(', ');
            }
            return String(variant);
          })
          .join('; ');
      } else if (typeof formattedItem.shopifyNativeVariant === 'object') {
        formattedItem.shopifyNativeVariantDisplay = Object.values(formattedItem.shopifyNativeVariant)
          .filter(v => v !== null && v !== undefined)
          .join(', ');
      }
    }

    if (formattedItem.product_category) {
      if (Array.isArray(formattedItem.product_category)) {
        formattedItem.product_category = formattedItem.product_category.join(', ');
      } else if (typeof formattedItem.product_category === 'string') {
        if (/([A-Z][a-z]+)(?:\1)+/.test(formattedItem.product_category)) {
          formattedItem.product_category = formattedItem.product_category
            .match(/[A-Z][a-z]+/g)
            ?.join(', ') || formattedItem.product_category;
        } 
        else if (formattedItem.product_category.includes(',')) {
          formattedItem.product_category = formattedItem.product_category
            .split(',')
            .map((item: string) => item.trim())
            .join(', ');
        }
      }
    }

    return formattedItem;
  });
}

export interface UpdateProductsParams {
  id: string;
  value: any;
}

type FilterCondition = {
  attribute: string;
  operator: string;
  value: string | number | boolean | (string | number | boolean)[];
};

type FilterGroup = FilterCondition[];

type Filters = FilterGroup[];

interface Product {
  data: any[];
  count: number;
  loading: boolean;
  error: string | null;
  filters: Filters;
  lastUpdatedProduct: any | null;
}

const initialState: Product = {
  data: [],
  filters: [],
  loading: false,
  error: null,
  count: 0,
  lastUpdatedProduct: null,
};

export const fetchProducts = createAsyncThunk(
  'product-sku',
  async ({ page = 1, limit = 25, q, fq }: BaseQueryParams, { rejectWithValue }) => {
    try {
      const queryParams = new URLSearchParams({
        page: String(page),
        limit: String(limit),
      });

      if (q) queryParams.append('q', q);
      if (fq) queryParams.append('fq', fq);
      const url = `/product-sku?${queryParams.toString()}`;
      const response = await apiClient.get(url);
      return response.data;
    } catch (err: any) {
      console.error('fetchProducts error:', err);
      return rejectWithValue(err.response?.data?.message || 'Fetch failed');
    }
  }
);

export const updateProduct = createAsyncThunk(
  'product-sku/update',
  async ({ id, value }: UpdateProductsParams, { rejectWithValue }) => {
    try {
      const response = await apiClient.patch(`/product-sku/${id}`, value);
      return response.data;
    } catch (err: any) {
      return rejectWithValue(err.response?.data?.message || 'Fetch failed');
    }
  }
);

const productSkuSlice = createSlice({
  name: 'ProductSku',
  initialState,
  reducers: {
    setProducts: (state, action: PayloadAction<any>) => {
      state.data = action.payload.data;
      state.count = action.payload.count;
    },
    setProductsFilters: (state, action: PayloadAction<Filters>) => {
      state.filters = action.payload;
    },
    resetFilter: (state) => {
      state.filters = [];
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.loading = false;
        const formattedData = formatComplexFields(action.payload.data);
        state.data = formattedData;
        state.count = action.payload.count;
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch products';
      })
      .addCase(updateProduct.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateProduct.fulfilled, (state, action) => {
        state.loading = false;
        const productIndex = state.data.findIndex((val) => val.id === action.payload.id);
        if (productIndex > -1) {
          const formattedProduct = formatComplexFields([action.payload])[0];
          state.data[productIndex] = formattedProduct;
        }
        state.lastUpdatedProduct = formatComplexFields([action.payload])[0];
      })
      .addCase(updateProduct.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update product';
      });
  },
});

export const { setProducts, setProductsFilters, resetFilter } = productSkuSlice.actions;
export default productSkuSlice.reducer;
