import React, { useEffect, useState } from 'react';
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Select,
  MenuItem,
  ListSubheader,
  InputLabel,
  FormControl,
  OutlinedInput,
  Grid,
  CircularProgress,
  Box,
  Chip,
} from '@mui/material';
import { FieldsType } from '@/redux-store/stores/common.store';
import SearchableSelect from '../SearchableSelect';

export const RenderValueField = ({ attr, handleValueChange, value, size }: any) => {
  if (!attr) return null;

  if (attr.type === 'multi_select' && !attr.fetch_db) {
    return (
      <FormControl fullWidth size={size}>
        <InputLabel>Value</InputLabel>
        <Select
          fullWidth
          multiple
          value={Array.isArray(value) ? value : []}
          onChange={e => handleValueChange(attr.key, e.target.value)}
          renderValue={selected => (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
              {(selected as string[]).map(value => (
                <Chip key={value} label={value} size="small" />
              ))}
            </Box>
          )}
          style={{ minWidth: 200 }}
        >
          {attr.options?.map((option: any) => (
            <MenuItem key={option.key || option.value} value={option.value}>
              {option.key || option.value}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    );
  }

  if (attr.key === 'parentSkuIds' || attr.key === 'childSkuIds') {
    return (
      <SearchableSelect
        multiple={true}
        attr={{
          ...attr,
          fetch_db: true,
          type: 'multi_select',
        }}
        value={value || []}
        serachUrl="product-sku"
        handleValueChange={(field: string, newValue: any) => {
          handleValueChange(attr.key, newValue);
        }}
        size={size}
      />
    );
  }

  if (attr.type === 'select' || attr.type === 'multi_select') {
    return (
      <SearchableSelect
        multiple={attr.type === 'multi_select'}
        attr={attr}
        value={value || (attr.type === 'multi_select' ? [] : '')}
        serachUrl={attr?.endpoint || 'product-sku'}
        handleValueChange={(field: string, newValue: any) => {
          handleValueChange(attr.key, newValue);
        }}
        size={size}
      />
    );
  }

  if (attr.type === 'text' || attr.type === 'number') {
    return (
      <TextField
        size={size}
        fullWidth
        type={attr.type === 'number' ? 'number' : 'text'}
        label={attr.label}
        inputProps={attr.type === 'number' ? { min: 0 } : {}}
        value={value || ''}
        onChange={e => handleValueChange(attr.key, e.target.value)}
      />
    );
  }

  return null;
};

function normalizeAttributes(input: any) {
  const normalizedAttributes: Record<string, any> = {};

  for (const [key, value] of Object.entries(input)) {
    if (Array.isArray(value) && value.every(v => typeof v === 'object' && v.value)) {
      normalizedAttributes[key] = value.map(v => v.value);
    } else {
      normalizedAttributes[key] = value;
    }
  }

  return normalizedAttributes;
}

export default function BulkUpdateModal({
  open,
  onClose,
  onApply,
  attributes,
}: {
  open: boolean;
  onClose: () => void;
  onApply: (data: any, resetValues: any) => void;
  attributes: FieldsType[] | undefined | any;
}) {
  const [selectedAttributes, setSelectedAttributes] = useState<string | undefined>();
  const [values, setValues] = useState<{ [key: string]: any }>({});
  const [loading, setLoading] = useState(false);
  let selectedAttrs: any = [];

  for (const [category, items] of Object.entries(attributes) as [string, FieldsType[]][]) {
    items &&
      items?.filter(attr => {
        if (selectedAttributes === attr?.key) selectedAttrs = [attr];
      });
  }

  const handleValueChange = (key: string, val: any) => {
    setValues(prevValues => ({
      ...prevValues,
      [key]: val,
    }));
  };

  // Reset values when selected attribute changes
  useEffect(() => {
    if (selectedAttributes) {
      setValues({});
    }
  }, [selectedAttributes]);

  const resetModalValues = () => {
    setSelectedAttributes(undefined);
    setValues({});
    setLoading(false);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Bulk Update SKUs</DialogTitle>
      <DialogContent>
        <FormControl fullWidth sx={{ mt: 2 }}>
          <InputLabel>Attributes</InputLabel>
          <Select
            value={selectedAttributes}
            onChange={e => {
              setSelectedAttributes(e.target.value);
            }}
            input={<OutlinedInput label="Attributes" />}
          >
            {Object.entries(attributes).map(([category, items]: [string, any]) => [
              <ListSubheader sx={{ fontSize: '1.5vh' }} color="primary" key={category}>
                {category}
              </ListSubheader>,
              ...items.map((attr: FieldsType) => (
                <MenuItem prefix="-" key={attr.key} sx={{ marginLeft: '2%' }} value={attr.key}>
                  {attr.label}
                </MenuItem>
              )),
            ])}
          </Select>
        </FormControl>

        <Grid container spacing={2} sx={{ mt: 2 }}>
          {selectedAttrs?.map((attr: FieldsType) => {
            const value =
              values[attr.key] !== undefined
                ? values[attr.key]
                : attr.type === 'multi_select'
                  ? []
                  : '';
            return (
              <Grid item xs={12} sm={6} key={attr.key}>
                <RenderValueField attr={attr} handleValueChange={handleValueChange} value={value} />
              </Grid>
            );
          })}
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={() => {
            resetModalValues();
            onClose();
          }}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          color="primary"
          onClick={() => {
            const normalizeData = normalizeAttributes(values);
            setLoading(true);
            onApply(normalizeData, resetModalValues);
          }}
          disabled={!selectedAttributes}
        >
          {loading ? <CircularProgress size={20} color="inherit" /> : 'Apply'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
