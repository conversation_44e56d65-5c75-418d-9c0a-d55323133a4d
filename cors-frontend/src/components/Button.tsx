"use client";
import { default as BaseButton, ButtonProps } from "@mui/material/Button";
import { RoleProtected } from "@/components/ProtectedRoleWrapper";
import { Actions, ActionsTarget, ActionsType } from "@/libs/casl/ability";

interface CustomButtonProps extends ButtonProps{
    title : string,
    ButtonAction ?: ActionsType,
    actionTarget ?: ActionsTarget
}

const Button = ({title,ButtonAction, actionTarget, ...rest} : CustomButtonProps) => {

  return (
    <>
      <RoleProtected
        action={ButtonAction}
        actionTarget={actionTarget}
      >
        <BaseButton {...rest}>
           {title}
        </BaseButton>
      </RoleProtected>
    </>
  );
};

export default Button;
