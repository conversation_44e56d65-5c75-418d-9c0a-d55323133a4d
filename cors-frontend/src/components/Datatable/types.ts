import { RootState } from "@/redux-store";
import { FieldsType } from "@/redux-store/stores/common.store";
import { ColumnDef } from "@tanstack/table-core";
import { SxProps, Theme } from "@mui/material";

export interface DataTableProps<T> {
  data: T[];
  columns: ColumnDef<T>[];
  loading?: boolean;
  title?: string;
  endpoint?: string;
  onRowClick?: (row: T) => void;
  searchPlaceholder?: string;
  enableFilters?: boolean;
  globalSearch?: boolean;
  totalCount?: number;
  initialPageSize?: number;
  initialPageIndex?: number;
  page?: number;
  limit?: number;
  onPageChange?: (page: number) => void;
  onLimitChange?: (limit: number) => void;
  serverSidePagination?: {
    totalCount: number;
    onPageChange: (page: number, pageSize: number) => void;
  };
  enableBulkUpdate?: boolean;
  onBulkApply?: ({ ids, attributes }: { ids: string[]; attributes: any }) => Promise<any>;
  reloadData?: () => void;
  bulkDataFields?: FieldsType[] | Record<string, FieldsType[]>;
  filterFields?: FieldsType[];
  filterCount?: number;
  storeName?: keyof RootState;
  onApplyFilters?: (normalizedFilters: any) => {};
  sx?: SxProps<Theme>;
}
