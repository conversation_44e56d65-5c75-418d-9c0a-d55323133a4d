import {
  Select,
  MenuItem,
  IconButton,
  Button,
  Stack,
  Box,
  Chip,
  Autocomplete,
  Typography,
  InputLabel,
  Divider,
  FormControl,
  TextField,
  debounce,
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { format } from 'date-fns';
import DeleteIcon from '@mui/icons-material/Delete';
import { RenderValueField } from './Modals/BulUpdateModal';
import { FieldsType } from '@/redux-store/stores/common.store';
import React, { useCallback, useState } from 'react';
import { toast } from 'react-toastify';
import apiClient from '@/utils/axios';
import { useDebounce } from 'react-use';
import { fetchShopifyVariants } from '@/actions';
import SearchableSelect from './SearchableSelect';
import { apiCall } from '@/actions/manual-order-actions';
import { SingleSKU } from '@/types/manual-order.type';

const filterEqData: any = {
  all: [
    { label: 'is', value: 'eq' },
    { label: 'is not', value: 'ne' },
    { label: 'contains', value: 'like' },
  ],
  number: [
    { label: 'is', value: 'eq' },
    { label: 'is not', value: 'ne' },
  ],
  date: [{ label: 'between', value: 'between' }],
  status: [
    { label: 'is', value: 'eq' },
    { label: 'is not', value: 'ne' },
  ],
  shopifyNativeVariant: [
    { label: 'is', value: 'json-eq' },
    { label: 'is not', value: 'json-ne' },
    { label: 'contains', value: 'json-like' },
  ],
};

const FilterGroup = ({ index, filter, onChange, onRemove, subIndex, columnsConfig }: any) => {
  const [shopifyVariants, setShopifyVariants] = useState<{ key: string; value: string }[]>([]);
  const [shopifyVariantsLoading, setShopifyVariantsLoading] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [skuOptions, setSkuOptions] = useState<Record<string, string>[]>([]);
  const [orderOptions, setOrderOptions] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const attributeValue = typeof filter.attribute === 'string' ? filter.attribute : '';
  const mainKey = attributeValue.split('.')[0] ?? attributeValue;
  let data = columnsConfig?.find((val: any) => val.key === filter.attribute);
  if (!data) {
    data = columnsConfig?.find((val: any) => val.key === mainKey);
  }

  const fetchOrders = async (term: string) => {
    if (!term || term.length < 2) {
      setOrderOptions([]);
      return;
    }

    try {
      setIsLoading(true);
      const urlParams = new URLSearchParams(window.location.search);
      const page = urlParams.get('page') || '1';
      const limit = urlParams.get('limit') || '25';
      const queryValue = `shopifyOrderNumber:like:${term}`;
      const encodedQuery = encodeURIComponent(queryValue);
      const response = await apiClient.get(`/orders?q=${encodedQuery}&page=${page}&limit=${limit}`);
      if (response.data && response.data.data) {
        const orders = response.data.data.map((order: any) => ({
          value: order.shopifyOrderNumber,
          label: `${order.shopifyOrderNumber}`,
        }));
        setOrderOptions(orders);
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
      toast.error('Failed to fetch orders');
    } finally {
      setIsLoading(false);
    }
  };
  // Debounce the search
  useDebounce(
    () => {
      if (filter.attribute === 'shopifyOrderNumber' && searchTerm) {
        fetchOrders(searchTerm);
      }
    },
    2000,
    [searchTerm],
  );

  // Initialize value properly for multi-select
  const value =
    filter.value !== ''
      ? data?.type === 'multi_select' && !Array.isArray(filter.value)
        ? [filter.value]
        : filter.value
      : data?.type === 'multi_select'
        ? []
        : '';

  // Force set operator to 'between' for orderDate
  React.useEffect(() => {
    if (filter.attribute === 'orderDate' && filter.operator !== 'between') {
      onChange(index, 'operator', 'between', subIndex);
    }
  }, [filter.attribute, filter.operator, index, subIndex, onChange]);

  // Get the appropriate filter operators based on attribute
  const getFilterOperators = () => {
    if (filter.attribute === 'orderDate') {
      return filterEqData.date;
    } else if (filter.attribute === 'priorities') {
      return filterEqData.number;
    } else if (filter.attribute === 'orderStatus') {
      return filterEqData.number;
    } else if (filter.attribute === 'shopifyOrderNumber') {
      return filterEqData.number;
    } else if (filter.attribute === 'shopifyNativeVariant') {
      return filterEqData.shopifyNativeVariant;
    } else if (filter.attribute === 'products_category') {
      return filterEqData.number;
    } else {
      return filterEqData[data?.type as 'number' | 'all'] ?? filterEqData.all;
    }
  };

  // Add a function to handle date range changes
  const handleDateRangeChange = (type: 'start' | 'end', date: Date | null) => {
    if (!date) return;

    const formattedDate = format(date, 'yyyy-MM-dd');
    const currentValue = filter.value || { start: '', end: '' };

    if (type === 'start' && currentValue.end) {
      const endDate = new Date(currentValue.end);
      if (date > endDate) {
        toast.error('Start date cannot be after end date');
        return;
      }
    } else if (type === 'end' && currentValue.start) {
      const startDate = new Date(currentValue.start);
      const startTimestamp = new Date(startDate.setHours(0, 0, 0, 0)).getTime();
      const endTimestamp = new Date(date.setHours(0, 0, 0, 0)).getTime();

      if (endTimestamp < startTimestamp) {
        toast.error('End date cannot be before start date');
        return;
      }
    }

    const newValue = {
      ...currentValue,
      [type]: formattedDate,
    };

    onChange(index, 'value', newValue, subIndex);
  };

  const fetchShopifyVariantsHandler = async () => {
    setShopifyVariantsLoading(true);
    try {
      const response = await fetchShopifyVariants();
      const formattedData = Object.entries(response)
        .map(([key, values]) =>
          (values as string[]).map(value => ({
            key,
            value,
          })),
        )
        .flat();
      setShopifyVariants(formattedData);
      setShopifyVariantsLoading(false);
    } catch (error) {
      setShopifyVariantsLoading(false);

      console.error('Error fetching shopify variants:', error);
      toast.error('Failed to fetch shopify variants');
    } finally {
      setShopifyVariantsLoading(false);
    }
  };

  const debouncedFetchSKUs = useCallback(
    debounce(async (inputValue: string) => {
      if (!inputValue) {
        setSkuOptions([]);
        return;
      }
      setIsLoading(true);
      try {
        const skus = await apiCall<{ data: SingleSKU[] }>(
          'get',
          `/product-sku?q=sku:like:${inputValue}`,
        );
        setSkuOptions(
          (skus?.data || []).map((elem: SingleSKU) => ({
            label: elem?.sku ?? '',
            value: elem?.sku ?? '',
          })),
        );
      } catch (error) {
        console.error('Error fetching SKUs:', error);
        setSkuOptions([]);
      } finally {
        setIsLoading(false);
      }
    }, 1000),
    [],
  );

  // First, let's add a function to handle product name searches
  const debouncedFetchProducts = useCallback(
    debounce(async (inputValue: string) => {
      if (!inputValue) {
        setSkuOptions([]);
        return;
      }
      setIsLoading(true);
      try {
        const response = await apiClient.get(`/product-sku/products?q=${inputValue}`);
        setSkuOptions(
          (response.data.data || []).map((product: { name: any }) => ({
            label: product.name,
            value: product.name,
          })),
        );
      } catch (error) {
        console.error('Error fetching products:', error);
        setSkuOptions([]);
      } finally {
        setIsLoading(false);
      }
    }, 1000),
    [],
  );

  return (
    <>
      <Grid container spacing={4}>
        <Grid size={12} display="flex" alignItems="center" justifyContent="space-between">
          <Typography fontSize={14} fontWeight={500}>
            Filter {index + 1 + '.' + subIndex}
          </Typography>
          <IconButton color="error" onClick={() => onRemove(index, subIndex)}>
            <DeleteIcon />
          </IconButton>
        </Grid>

        <Grid size={6}>
          <FormControl fullWidth size="small">
            <InputLabel>Attribute</InputLabel>
            <Select
              value={mainKey}
              onChange={e => {
                onChange(index, 'value', '', subIndex);
                if (e.target.value === 'shopifyNativeVariant') {
                  fetchShopifyVariantsHandler();
                }
                const data = columnsConfig?.find((val: any) => {
                  if (val.key === e.target.value) return val;
                });
                onChange(
                  index,
                  'attribute',
                  `${e.target.value}${data?.secondary_key ? `.${data.secondary_key}` : ''}`,
                  subIndex,
                );
              }}
              label="Attribute"
            >
              {columnsConfig?.map((column: { key: string; label: string }) => {
                return (
                  <MenuItem key={column.key} value={column.key}>
                    {column.label}
                  </MenuItem>
                );
              })}
            </Select>
          </FormControl>
        </Grid>
        {filter.attribute !== 'shopifyNativeVariant' && (
          <Grid size={6}>
            {filter.attribute === 'orderDate' ? (
              <FormControl fullWidth size="small">
                <InputLabel>Operator</InputLabel>
                <Select
                  fullWidth
                  value="between"
                  disabled
                  displayEmpty
                  renderValue={() => 'between'}
                >
                  <MenuItem value="between">between</MenuItem>
                </Select>{' '}
              </FormControl>
            ) : filter.attribute !== 'shopifyNativeVariant' ? (
              <FormControl fullWidth size="small">
                <InputLabel>Operator</InputLabel>
                <Select
                  fullWidth
                  value={filter.operator}
                  onChange={e => onChange(index, 'operator', e.target.value, subIndex)}
                  label="Operator"
                >
                  {getFilterOperators().map((type: any) => (
                    <MenuItem key={type.label} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            ) : null}
          </Grid>
        )}
        {filter.attribute && (
          <>
            {filter.attribute === 'orderDate' ? (
              // For orderDate, show date pickers
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <Stack direction="row" spacing={2} sx={{ minWidth: 300 }}>
                  <DatePicker
                    label="Start Date"
                    value={filter.value?.start ? new Date(filter.value.start) : null}
                    onChange={date => handleDateRangeChange('start', date)}
                    slotProps={{
                      textField: { size: 'small' },
                    }}
                  />
                  <DatePicker
                    label="End Date"
                    value={filter.value?.end ? new Date(filter.value.end) : null}
                    onChange={date => handleDateRangeChange('end', date)}
                    slotProps={{
                      textField: { size: 'small' },
                    }}
                  />
                </Stack>
              </LocalizationProvider>
            ) : filter.attribute === 'shopifyOrderNumber' ? (
              // For shopifyOrderNumber, show searchable multi-select
              <Autocomplete
                size="small"
                fullWidth
                multiple
                style={{ minWidth: 200 }}
                options={orderOptions}
                getOptionLabel={(option: any) => option.label || ''}
                renderInput={params => <TextField {...params} label="Search Order" />}
                onInputChange={(_, newValue) => {
                  setSearchTerm(newValue || '');
                }}
                onChange={(e, newValue) => {
                  onChange(
                    index,
                    'value',
                    newValue?.map(v => v.value),
                    subIndex,
                  );
                }}
                loading={shopifyVariantsLoading}
              />
            ) : filter.attribute === 'priorities' || filter.attribute === 'orderStatus' ? (
              // For priorities and status, show multiselect
              <FormControl fullWidth size="small">
                <InputLabel>Value</InputLabel>
                <Select
                  multiple
                  fullWidth
                  label="Value"
                  value={Array.isArray(value) ? value : []}
                  onChange={e => onChange(index, 'value', e.target.value, subIndex)}
                  renderValue={selected => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {(selected as string[]).map(value => (
                        <Chip key={value} label={value} />
                      ))}
                    </Box>
                  )}
                >
                  {data.options?.map((option: any) => (
                    <MenuItem key={option.key || option.value} value={option.value}>
                      {option.key || option.value}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            ) : filter.attribute === 'shopifyNativeVariant' ? (
              <>
                <Grid size={6}>
                  <Autocomplete
                    size="small"
                    style={{ minWidth: 200 }}
                    value={value?.key || ''}
                    options={Array.from(new Set(shopifyVariants.map(v => v.key)))}
                    getOptionLabel={(option: string) => option}
                    renderInput={params => <TextField {...params} label="Select Key" />}
                    onChange={(_, newValue) => {
                      onChange(index, 'value', { key: newValue, value: '' }, subIndex);
                    }}
                    loading={shopifyVariantsLoading}
                  />
                </Grid>
                <Grid size={6}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Operator</InputLabel>
                    <Select
                      fullWidth
                      value={filter.operator || ''}
                      onChange={e => onChange(index, 'operator', e.target.value, subIndex)}
                      label="Operator"
                    >
                      {getFilterOperators().map((type: any) => (
                        <MenuItem key={type.label} value={type.value}>
                          {type.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid size={6}>
                  <Autocomplete
                    freeSolo
                    size="small"
                    value={value?.value || ''}
                    options={shopifyVariants.filter(v => v.key === value?.key).map(v => v.value)}
                    getOptionLabel={(option: string) => option}
                    renderInput={params => <TextField {...params} label="Value" />}
                    onChange={(_, newValue) => {
                      onChange(index, 'value', { ...value, value: newValue }, subIndex);
                    }}
                    onInputChange={(_, newInputValue) => {
                      onChange(index, 'value', { ...value, value: newInputValue }, subIndex);
                    }}
                    loading={shopifyVariantsLoading}
                  />
                </Grid>
              </>
            ) : filter.attribute === 'sku' ||
              filter.attribute === 'parentSku.parentSku' ||
              filter.attribute === 'childSku.childSku' ||
              filter.attribute === 'products.name' ? (
              <Autocomplete
                size="small"
                fullWidth
                multiple
                style={{ minWidth: 200 }}
                options={skuOptions}
                getOptionLabel={(option: any) => option.label || ''}
                renderInput={params => (
                  <TextField
                    {...params}
                    label={
                      filter.attribute === 'sku'
                        ? 'Search SKU'
                        : filter.attribute === 'childSku.childSku'
                          ? 'Search Child SKU'
                          : filter.attribute === 'products.name'
                            ? 'Search Product Name'
                            : 'Search Parent SKU'
                    }
                    placeholder={
                      filter.attribute === 'sku'
                        ? 'Type to search SKUs...'
                        : filter.attribute === 'childSku.childSku'
                          ? 'Type to search child SKUs...'
                          : filter.attribute === 'products.name'
                            ? 'Type to search products...'
                            : 'Type to search parent SKUs...'
                    }
                  />
                )}
                onInputChange={(_, newValue) => {
                  if (filter.attribute === 'products.name') {
                    debouncedFetchProducts(newValue || '');
                  } else {
                    debouncedFetchSKUs(newValue || '');
                  }
                }}
                onChange={(e, newValue) => {
                  onChange(
                    index,
                    'value',
                    Array.isArray(newValue)
                      ? newValue.map(item => {
                          return item.value;
                        })
                      : [],
                    subIndex,
                  );
                }}
                loading={isLoading}
              />
            ) : data?.type === 'multi_select' ? (
              // For multi-select fields
              <FormControl fullWidth size="small">
                <InputLabel>Value</InputLabel>
                <Select
                  fullWidth
                  label="Value"
                  multiple
                  value={Array.isArray(value) ? value : []}
                  onChange={e => onChange(index, 'value', e.target.value, subIndex)}
                  renderValue={selected => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {(selected as string[]).map(value => (
                        <Chip key={value} label={value} />
                      ))}
                    </Box>
                  )}
                >
                  {data.options?.map((option: any) => (
                    <MenuItem key={option.key || option.value} value={option.value}>
                      {option.key || option.value}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            ) : (
              <RenderValueField
                attr={data}
                handleValueChange={(_: any, e: string | boolean | number) =>
                  onChange(index, 'value', e, subIndex)
                }
                value={value}
                size="small"
              />
            )}
          </>
        )}
      </Grid>
    </>
  );
};

export default function FilterComponent({
  filters,
  columnsConfig,
  handleAdd,
  handleChange,
  handleRemove,
}: any) {
  return (
    <div>
      <Stack spacing={1}>
        {filters.map((filter: FieldsType[], index: number) => (
          <>
            <React.Fragment key={`filter-group-${index}`}>
              {filter.map((attributes: FieldsType, subIndex: number) => (
                <>
                  <FilterGroup
                    key={`filter-item-${index}-${subIndex}`}
                    subIndex={subIndex}
                    index={index}
                    filter={attributes}
                    columnsConfig={columnsConfig}
                    onChange={handleChange}
                    onRemove={handleRemove}
                  />{' '}
                </>
              ))}
              <div className="flex justify-end">
                <Button
                  onClick={() => handleAdd(index)}
                  variant="text"
                  sx={{ width: 'fit-content', margin: '10px 0 10px 0' }}
                >
                  AND
                </Button>
              </div>
            </React.Fragment>{' '}
            <Divider sx={{ marginTop: 2, marginBottom: 2 }} />
          </>
        ))}
      </Stack>
      <div className="flex justify-center">
        <Button
          onClick={() => handleAdd()}
          variant="outlined"
          sx={{ width: 'fit-content', margin: '10px 0 10px 0' }}
        >
          OR
        </Button>
      </div>
    </div>
  );
}
