'use server';

import apiClient from '@/utils/axios';

export async function apiCall<T>(
  method: string,
  endpoint: string,
  data?: any,
  params?: any,
): Promise<T> {
  try {
    const response = await apiClient({
      method,
      url: endpoint,
      data,
      params,
    });
    console.log(response.data);
    return response?.data;
  } catch (error) {
    console.error(`API Error (${endpoint}):`, error);
    throw error;
  }
}
