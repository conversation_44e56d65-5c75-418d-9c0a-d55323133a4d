import { Suspense } from 'react';
import LoadingView from '@/components/LoadingView';
import OrdersWrapper from '@/views/order-management/orders';
import { fetchOrders } from '@/actions/orders';

const OrdersData = async ({ page, limit }: { page: number; limit: number }) => {
  try {
    const [unflaggedData, flaggedData] = await Promise.all([
      fetchOrders({ page, limit, fq: 'flagged:eq:false' }),
      fetchOrders({ page, limit, fq: 'flagged:eq:true' }),
    ]);

    return (
      <OrdersWrapper
        data={{
          unflagged: unflaggedData,
          flagged: flaggedData,
        }}
        page={page}
        limit={limit}
      />
    );
  } catch (error) {
    return <div>Failed to load data. Please try again later.</div>;
  }
};

const OrderIntaketPage = async (Props: { searchParams: any }) => {
  const { searchParams } = Props;
  const { page = 1, limit = 25, q, fq } = await searchParams;
  return (
    <Suspense fallback={<LoadingView />}>
      <OrdersData page={Number(page)} limit={Number(limit)} />
    </Suspense>
  );
};

export default OrderIntaketPage;
