import { Suspense } from 'react';
import LoadingView from '@/components/LoadingView';
import { PageProps } from '../../../../../.next/types/app/(dashboard)/ordermanagement/products/page';
import QueuesWrapper from '@/views/order-management/queues';

const QueuesManagementData = async ({ page, limit }: { page: number; limit: number }) => {
  try {
    return <QueuesWrapper />;
  } catch (error) {
    console.error('Failed to fetch data:', error);
    return <div>Failed to load data. Please try again later.</div>;
  }
};

const QueuesManagementPage = async (Props: PageProps) => {
  const { searchParams } = Props;
  const { page = 1, limit = 25, q, fq } = await searchParams;
  return (
    <Suspense fallback={<LoadingView />}>
      <QueuesManagementData page={page} limit={limit} />
    </Suspense>
  );
};

export default QueuesManagementPage;
