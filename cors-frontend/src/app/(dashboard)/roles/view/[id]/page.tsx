import RoleViewForm from '@views/roles/RoleViewForm'
import apiClient from '@/utils/axios'

export default async function RoleViewPage({ params }: { params: Promise<{ id: string }> }) {
  // Get the session using getServerSession
  
  // Create authenticated API instance
  
  // Await params before using its properties
  const resolvedParams = await params
  const id = resolvedParams.id
  
  try {
    // Fetch data
    const permissionsResponse = await apiClient.get('/roles/permissions')
    const roleResponse = await apiClient.get(`/roles/${id}`)
    
    // Return the component with data
    return (
      <RoleViewForm 
        permissions={permissionsResponse.data} 
        roleData={roleResponse.data} 
      />
    )
  } catch (error) {
    console.error('Failed to fetch data:', error)
    return <div>Failed to load data. Please try again later.</div>
  }
}
