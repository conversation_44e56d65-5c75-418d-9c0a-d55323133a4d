import { Suspense } from 'react'
import RoleAddForm from '@/views/roles/RoleAddForm'
import LoadingView from '@/components/LoadingView'
import apiClient from '@/utils/axios'

const RoleEditData = async ({ id }: { id: string }) => {

  try {
    const permissionsResponse = await apiClient.get('/roles/permissions')
    
    const roleResponse = await apiClient.get(`/roles/${id}`)
    
    return <RoleAddForm permissions={permissionsResponse.data} roleData={roleResponse.data} isEdit={true} />
  } catch (error) {
    console.error('Failed to fetch data:', error)
    return <div>Failed to load data. Please try again later.</div>
  }
}

const RoleEditPage = async ({ params }: { params: Promise<{ id: string }> }) => {
  const resolvedParams = await params
  const id = resolvedParams.id
  
  return (
    <Suspense fallback={<LoadingView />}>
      <RoleEditData id={id} />
    </Suspense>
  )
}

export default RoleEditPage
