export interface QueueItem {
  id: string;
  priority: 'high' | 'medium' | 'low';
  filename: string;
  cropType: string;
  orderNumber: string;
  orderDate: string;
  assignedTo: string;
  imageUrl?: string;
  status?: string;
  productType?: string;
  customerName?: string;
  notes?: string;
}

export const mockQueueData = {
  'crop-review': {
    count: 5,
    items: [
      {
        id: '1',
        priority: 'high',
        filename: 'product_123_crop1.jpg',
        cropType: 'Auto Crop',
        orderNumber: 'ORD-001',
        orderDate: '2024-03-20',
        assignedTo: '<PERSON>',
        imageUrl: 'https://picsum.photos/200/200',
        productType: 'T-Shirt',
        customerName: '<PERSON>',
        notes: 'Auto-cropped using CutoutPro'
      },
      {
        id: '2',
        priority: 'medium',
        filename: 'product_124_crop1.jpg',
        cropType: 'Auto Crop',
        orderNumber: 'ORD-002',
        orderDate: '2024-03-20',
        assignedTo: '',
        imageUrl: 'https://picsum.photos/200/201',
        productType: 'Mug',
        customerName: '<PERSON>',
        notes: 'Auto-cropped using CutoutPro'
      }
    ]
  },
  'crop-needed': {
    count: 3,
    items: [
      {
        id: '3',
        priority: 'high',
        filename: 'product_125_crop1.jpg',
        cropType: 'Manual Crop',
        orderNumber: 'ORD-003',
        orderDate: '2024-03-20',
        assignedTo: 'Jane Smith',
        imageUrl: 'https://picsum.photos/200/202',
        productType: 'Canvas Print',
        customerName: 'Charlie Brown',
        notes: 'Auto-crop rejected, needs manual intervention'
      }
    ]
  },
  'template-placement': {
    count: 2,
    items: [
      {
        id: '4',
        priority: 'medium',
        filename: 'product_126_template1.jpg',
        cropType: 'Approved',
        orderNumber: 'ORD-004',
        orderDate: '2024-03-20',
        assignedTo: '',
        imageUrl: 'https://picsum.photos/200/203',
        productType: 'Phone Case',
        customerName: 'David Wilson',
        notes: 'Ready for template placement'
      }
    ]
  },
  'ready-for-artwork': {
    count: 4,
    items: [
      {
        id: '5',
        priority: 'low',
        filename: 'product_127_artwork1.jpg',
        cropType: 'Approved',
        orderNumber: 'ORD-005',
        orderDate: '2024-03-20',
        assignedTo: 'Mike Johnson',
        imageUrl: 'https://picsum.photos/200/204',
        productType: 'Business Card',
        customerName: 'Eve Adams',
        notes: 'Ready for artwork creation'
      }
    ]
  },
  'revision-artwork': {
    count: 1,
    items: [
      {
        id: '6',
        priority: 'high',
        filename: 'product_128_revision1.jpg',
        cropType: 'Approved',
        orderNumber: 'ORD-006',
        orderDate: '2024-03-20',
        assignedTo: 'Sarah Parker',
        imageUrl: 'https://picsum.photos/200/205',
        productType: 'Poster',
        customerName: 'Frank Miller',
        notes: 'Customer requested color changes'
      }
    ]
  }
}; 