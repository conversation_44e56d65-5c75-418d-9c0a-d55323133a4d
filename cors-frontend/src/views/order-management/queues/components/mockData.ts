export interface QueueItem {
  id: string;
  priority: 'high' | 'medium' | 'low';
  priorityLabel?: string; // e.g., "Socks Rush", "Pajamas Rush", "Blanket Rush"
  filename: string;
  cropType: string;
  orderNumber: string;
  orderDate: string;
  assignedTo: string;
  imageUrl?: string;
  status?: string;
  productType?: string;
  customerName?: string;
  notes?: string;
}

export const mockQueueData = {
  'crop-review': {
    count: 5,
    items: [
      {
        id: '1',
        priority: 'high',
        filename: 'product_123_crop1.jpg',
        cropType: 'Auto Crop',
        orderNumber: 'ORD-001',
        orderDate: '2024-03-20',
        assignedTo: '<PERSON>',
        imageUrl: 'https://picsum.photos/200/200',
        productType: 'T-Shirt',
        customerName: '<PERSON>',
        notes: 'Auto-cropped using CutoutPro'
      },
      {
        id: '2',
        priority: 'medium',
        filename: 'product_124_crop1.jpg',
        cropType: 'Auto Crop',
        orderNumber: 'ORD-002',
        orderDate: '2024-03-20',
        assignedTo: '',
        imageUrl: 'https://picsum.photos/200/201',
        productType: 'Mug',
        customerName: '<PERSON>',
        notes: 'Auto-cropped using CutoutPro'
      }
    ]
  },
  'crop-needed': {
    count: 8,
    items: [
      {
        id: '3',
        priority: 'high',
        priorityLabel: 'Socks Rush',
        filename: 'socks_design_001.jpg',
        cropType: 'Face Cropped',
        orderNumber: 'ORD-003',
        orderDate: '2024-03-20',
        assignedTo: 'Jane Smith',
        imageUrl: 'https://picsum.photos/200/202',
        productType: 'Socks',
        customerName: 'Charlie Brown',
        notes: 'Auto-crop rejected, needs manual intervention'
      },
      {
        id: '4',
        priority: 'high',
        priorityLabel: 'Pajamas Rush',
        filename: 'pajama_print_002.jpg',
        cropType: 'Background Removal',
        orderNumber: 'ORD-004',
        orderDate: '2024-03-21',
        assignedTo: 'Mike Johnson',
        imageUrl: 'https://picsum.photos/200/203',
        productType: 'Pajamas',
        customerName: 'Sarah Wilson',
        notes: 'Customer requested background removal'
      },
      {
        id: '5',
        priority: 'high',
        priorityLabel: 'Blanket Rush',
        filename: 'blanket_design_003.jpg',
        cropType: 'Face Cropped',
        orderNumber: 'ORD-005',
        orderDate: '2024-03-21',
        assignedTo: 'Emily Davis',
        imageUrl: 'https://picsum.photos/200/204',
        productType: 'Blanket',
        customerName: 'Robert Taylor',
        notes: 'High priority rush order'
      },
      {
        id: '6',
        priority: 'medium',
        priorityLabel: 'T-Shirt Standard',
        filename: 'tshirt_design_004.jpg',
        cropType: 'Background Removal',
        orderNumber: 'ORD-006',
        orderDate: '2024-03-22',
        assignedTo: 'Alex Thompson',
        imageUrl: 'https://picsum.photos/200/205',
        productType: 'T-Shirt',
        customerName: 'Lisa Anderson',
        notes: 'Standard processing'
      },
      {
        id: '7',
        priority: 'medium',
        priorityLabel: 'Mug Standard',
        filename: 'mug_photo_005.jpg',
        cropType: 'Face Cropped',
        orderNumber: 'ORD-007',
        orderDate: '2024-03-22',
        assignedTo: '',
        imageUrl: 'https://picsum.photos/200/206',
        productType: 'Mug',
        customerName: 'David Martinez',
        notes: 'Awaiting assignment'
      },
      {
        id: '8',
        priority: 'low',
        priorityLabel: 'Canvas Standard',
        filename: 'canvas_art_006.jpg',
        cropType: 'Background Removal',
        orderNumber: 'ORD-008',
        orderDate: '2024-03-23',
        assignedTo: 'Jennifer Lee',
        imageUrl: 'https://picsum.photos/200/207',
        productType: 'Canvas',
        customerName: 'Michelle Garcia',
        notes: 'Low priority order'
      },
      {
        id: '9',
        priority: 'high',
        priorityLabel: 'Phone Case Rush',
        filename: 'phonecase_design_007.jpg',
        cropType: 'Face Cropped',
        orderNumber: 'ORD-009',
        orderDate: '2024-03-23',
        assignedTo: 'Chris Wilson',
        imageUrl: 'https://picsum.photos/200/208',
        productType: 'Phone Case',
        customerName: 'Kevin Brown',
        notes: 'Rush delivery requested'
      },
      {
        id: '10',
        priority: 'medium',
        priorityLabel: 'Poster Standard',
        filename: 'poster_image_008.jpg',
        cropType: 'Background Removal',
        orderNumber: 'ORD-010',
        orderDate: '2024-03-24',
        assignedTo: 'Amanda Clark',
        imageUrl: 'https://picsum.photos/200/209',
        productType: 'Poster',
        customerName: 'Jessica Miller',
        notes: 'Standard processing timeline'
      }
    ]
  },
  'template-placement': {
    count: 2,
    items: [
      {
        id: '4',
        priority: 'medium',
        filename: 'product_126_template1.jpg',
        cropType: 'Approved',
        orderNumber: 'ORD-004',
        orderDate: '2024-03-20',
        assignedTo: '',
        imageUrl: 'https://picsum.photos/200/203',
        productType: 'Phone Case',
        customerName: 'David Wilson',
        notes: 'Ready for template placement'
      }
    ]
  },
  'ready-for-artwork': {
    count: 4,
    items: [
      {
        id: '5',
        priority: 'low',
        filename: 'product_127_artwork1.jpg',
        cropType: 'Approved',
        orderNumber: 'ORD-005',
        orderDate: '2024-03-20',
        assignedTo: 'Mike Johnson',
        imageUrl: 'https://picsum.photos/200/204',
        productType: 'Business Card',
        customerName: 'Eve Adams',
        notes: 'Ready for artwork creation'
      }
    ]
  },
  'revision-artwork': {
    count: 1,
    items: [
      {
        id: '6',
        priority: 'high',
        filename: 'product_128_revision1.jpg',
        cropType: 'Approved',
        orderNumber: 'ORD-006',
        orderDate: '2024-03-20',
        assignedTo: 'Sarah Parker',
        imageUrl: 'https://picsum.photos/200/205',
        productType: 'Poster',
        customerName: 'Frank Miller',
        notes: 'Customer requested color changes'
      }
    ]
  }
}; 