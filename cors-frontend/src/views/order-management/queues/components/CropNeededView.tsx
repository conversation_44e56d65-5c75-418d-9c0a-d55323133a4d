import React from 'react';
import { Box, Typography, Grid, Card, CardContent, Chip, CardActionArea, CardMedia, Stack, Tooltip } from '@mui/material';
import { QueueItem } from './mockData';

interface CropNeededViewProps {
  data: QueueItem[];
  loading: boolean;
}

const CropNeededView: React.FC<CropNeededViewProps> = ({ data, loading }) => {
  // Group items by priority
  const groupedItems = data.reduce((acc, item) => {
    const priority = item.priority;
    if (!acc[priority]) {
      acc[priority] = [];
    }
    acc[priority].push(item);
    return acc;
  }, {} as Record<string, QueueItem[]>);

  // Sort priorities: high -> medium -> low
  const sortedPriorities = Object.keys(groupedItems).sort((a, b) => {
    const priorityOrder = { high: 0, medium: 1, low: 2 };
    return priorityOrder[a as keyof typeof priorityOrder] - priorityOrder[b as keyof typeof priorityOrder];
  });

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'success';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <Typography>Loading...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 2 }}>
      {sortedPriorities.map(priority => (
        <Box key={priority} sx={{ mb: 4 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            mb: 2,
            px: 2,
            py: 1,
            bgcolor: 'background.paper',
            borderRadius: 1,
            boxShadow: 1
          }}>
            <Typography variant="h6" sx={{ fontWeight: 500, textTransform: 'capitalize', mr: 2 }}>
              {priority} Priority
            </Typography>
            <Chip
              label={`${groupedItems[priority].length} items`}
              size="small"
              color={getPriorityColor(priority)}
              variant="outlined"
            />
          </Box>
          <Grid container spacing={2}>
            {groupedItems[priority].map(item => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={item.id}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: (theme) => theme.shadows[4],
                    },
                    border: '1px solid',
                    borderColor: 'divider',
                  }}
                >
                  <CardActionArea>
                    <CardMedia
                      component="img"
                      height="180"
                      image={item.imageUrl || '/placeholder-image.jpg'}
                      alt={item.filename}
                      sx={{
                        objectFit: 'cover',
                        borderBottom: '1px solid',
                        borderColor: 'divider',
                      }}
                    />
                    <CardContent sx={{ flexGrow: 1, p: 2 }}>
                      <Stack spacing={1.5}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Tooltip title={item.filename}>
                            <Typography 
                              variant="subtitle1" 
                              noWrap 
                              sx={{ 
                                fontWeight: 500,
                                maxWidth: '70%',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis'
                              }}
                            >
                              {item.filename}
                            </Typography>
                          </Tooltip>
                          <Chip
                            label={priority}
                            size="small"
                            color={getPriorityColor(priority)}
                            sx={{ ml: 1 }}
                          />
                        </Box>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                          <Tooltip title={item.cropType}>
                            <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                              <Box component="span" sx={{ fontWeight: 500, color: 'text.primary', mr: 1, minWidth: '80px' }}>
                                Crop Type:
                              </Box>
                              <Box sx={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                {item.cropType}
                              </Box>
                            </Typography>
                          </Tooltip>
                          <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                            <Box component="span" sx={{ fontWeight: 500, color: 'text.primary', mr: 1, minWidth: '80px' }}>
                              Order #:
                            </Box>
                            {item.orderNumber}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                            <Box component="span" sx={{ fontWeight: 500, color: 'text.primary', mr: 1, minWidth: '80px' }}>
                              Date:
                            </Box>
                            {new Date(item.orderDate).toLocaleDateString()}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                            <Box component="span" sx={{ fontWeight: 500, color: 'text.primary', mr: 1, minWidth: '80px' }}>
                              Assigned:
                            </Box>
                            {item.assignedTo || 'Unassigned'}
                          </Typography>
                        </Box>
                        <Box sx={{ mt: 1, display: 'flex', justifyContent: 'flex-end' }}>
                          <Chip
                            label="Process"
                            color="primary"
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              console.log('Process', item.id);
                            }}
                            sx={{
                              '&:hover': {
                                backgroundColor: 'primary.dark',
                              },
                            }}
                          />
                        </Box>
                      </Stack>
                    </CardContent>
                  </CardActionArea>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      ))}
    </Box>
  );
};

export default CropNeededView;
