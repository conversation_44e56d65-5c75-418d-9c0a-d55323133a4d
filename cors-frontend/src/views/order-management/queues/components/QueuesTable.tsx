import React, { useMemo } from 'react';
import { createColumnHelper, ColumnDef } from '@tanstack/react-table';
import { Typography, Chip, Box, CircularProgress, Tooltip, IconButton } from '@mui/material';
import DataTable from '@/components/Datatable';
import InfoIcon from '@mui/icons-material/Info';
import { QueueItem } from './mockData';

interface QueuesTableProps {
  data: QueueItem[];
  loading: boolean;
  queueType: string;
}

const columnHelper = createColumnHelper<QueueItem>();

const QueuesTable: React.FC<QueuesTableProps> = ({ data, loading, queueType }) => {
  const columns = useMemo<ColumnDef<QueueItem, any>[]>(
    () => [
      columnHelper.accessor('priority', {
        header: 'Priority',
        enableSorting: false,
        cell: ({ getValue }) => {
          const value = getValue();
          const color = value === 'high' ? 'error' : value === 'medium' ? 'warning' : 'success';
          return (
            <Chip
              label={value.charAt(0).toUpperCase() + value.slice(1)}
              size="small"
              color={color}
              variant="tonal"
            />
          );
        },
      }),
      columnHelper.accessor('imageUrl', {
        header: 'Image',
        enableSorting: false,
        cell: ({ getValue, row }) => (
          <Box
            component="img"
            src={getValue() || '/placeholder-image.jpg'}
            alt={row.original.filename}
            sx={{
              width: 60,
              height: 60,
              objectFit: 'cover',
              borderRadius: 1,
              cursor: 'pointer',
              '&:hover': {
                transform: 'scale(1.1)',
                transition: 'transform 0.2s ease-in-out',
              },
            }}
          />
        ),
      }),
      columnHelper.accessor('filename', {
        header: 'Filename',
        enableSorting: false,
        cell: info => (
          <Typography noWrap sx={{ maxWidth: 200 }}>
            {info.getValue()}
          </Typography>
        ),
      }),
      columnHelper.accessor('productType', {
        header: 'Product Type',
        enableSorting: false,
        cell: info => info.getValue(),
      }),
      columnHelper.accessor('orderNumber', {
        header: 'Order #',
        enableSorting: false,
        cell: info => info.getValue(),
      }),
      columnHelper.accessor('customerName', {
        header: 'Customer',
        enableSorting: false,
        cell: info => info.getValue(),
      }),
      columnHelper.accessor('orderDate', {
        header: 'Order Date',
        enableSorting: false,
        cell: info => new Date(info.getValue()).toLocaleDateString(),
      }),
      columnHelper.accessor('assignedTo', {
        header: 'Assigned To',
        enableSorting: false,
        cell: info => info.getValue() || 'Unassigned',
      }),
      columnHelper.accessor('notes', {
        header: 'Notes',
        enableSorting: false,
        cell: ({ getValue }) => {
          const notes = getValue();
          return notes ? (
            <Tooltip title={notes}>
              <IconButton size="small">
                <InfoIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          ) : null;
        },
      }),
      columnHelper.display({
        id: 'actions',
        header: 'Actions',
        enableSorting: false,
        cell: ({ row }) => {
          const getActionButton = () => {
            switch (queueType) {
              case 'crop-review':
                return (
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Chip
                      label="Approve"
                      color="success"
                      size="small"
                      onClick={() => handleApprove(row.original.id)}
                    />
                    <Chip
                      label="Reject"
                      color="error"
                      size="small"
                      onClick={() => handleReject(row.original.id)}
                    />
                  </Box>
                );
              case 'crop-needed':
                return (
                  <Chip
                    label="Process"
                    color="primary"
                    size="small"
                    onClick={() => handleProcess(row.original.id)}
                  />
                );
              case 'template-placement':
                return (
                  <Chip
                    label="Place Template"
                    color="primary"
                    size="small"
                    onClick={() => handleTemplatePlace(row.original.id)}
                  />
                );
              case 'ready-for-artwork':
                return (
                  <Chip
                    label="Create Artwork"
                    color="primary"
                    size="small"
                    onClick={() => handleCreateArtwork(row.original.id)}
                  />
                );
              case 'revision-artwork':
                return (
                  <Chip
                    label="Revise"
                    color="primary"
                    size="small"
                    onClick={() => handleRevise(row.original.id)}
                  />
                );
              default:
                return null;
            }
          };

          return getActionButton();
        },
      }),
    ],
    [queueType],
  );

  // Action handlers - implement these based on your requirements
  const handleApprove = (id: string) => console.log('Approve', id);
  const handleReject = (id: string) => console.log('Reject', id);
  const handleProcess = (id: string) => console.log('Process', id);
  const handleTemplatePlace = (id: string) => console.log('Place Template', id);
  const handleCreateArtwork = (id: string) => console.log('Create Artwork', id);
  const handleRevise = (id: string) => console.log('Revise', id);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <DataTable
      columns={columns}
      data={data}
    />
  );
};

export default QueuesTable;
