import { Box, Tab, Tabs, Badge } from '@mui/material';
import React, { useState, useEffect } from 'react';
import QueuesTable from './QueuesTable';
import CropNeededView from './CropNeededView';
import { mockQueueData, QueueItem } from './mockData';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

interface QueueData {
  count: number;
  data: QueueItem[];
  loading: boolean;
}

interface QueueState {
  cropReview: QueueData;
  cropNeeded: QueueData;
  templatePlacement: QueueData;
  readyForArtwork: QueueData;
  revisionArtwork: QueueData;
}

const QueuesTabs = () => {
  const [queueData, setQueueData] = useState<QueueState>({
    cropReview: { count: 0, data: [], loading: true },
    cropNeeded: { count: 0, data: [], loading: true },
    templatePlacement: { count: 0, data: [], loading: true },
    readyForArtwork: { count: 0, data: [], loading: true },
    revisionArtwork: { count: 0, data: [], loading: true },
  });

  const fetchQueueData = async (queueType: string): Promise<QueueData> => {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Use mock data for now
      const mockData = mockQueueData[queueType as keyof typeof mockQueueData];
      return { 
        count: mockData.count, 
        data: mockData.items as QueueItem[], 
        loading: false 
      };
    } catch (error) {
      console.error(`Error fetching ${queueType} queue data:`, error);
      return { count: 0, data: [], loading: false };
    }
  };

  useEffect(() => {
    const loadQueueData = async () => {
      const [cropReview, cropNeeded, templatePlacement, readyForArtwork, revisionArtwork] =
        await Promise.all([
          fetchQueueData('crop-review'),
          fetchQueueData('crop-needed'),
          fetchQueueData('template-placement'),
          fetchQueueData('ready-for-artwork'),
          fetchQueueData('revision-artwork'),
        ]);

      setQueueData({
        cropReview,
        cropNeeded,
        templatePlacement,
        readyForArtwork,
        revisionArtwork,
      });
    };

    loadQueueData();
  }, []);

  const tabs = [
    {
      label: 'Crop Review',
      count: queueData.cropReview.count,
      content: (
        <QueuesTable
          data={queueData.cropReview.data}
          loading={queueData.cropReview.loading}
          queueType="crop-review"
        />
      ),
    },
    {
      label: 'Crop Needed',
      count: queueData.cropNeeded.count,
      content: (
        <CropNeededView
          data={queueData.cropNeeded.data}
          loading={queueData.cropNeeded.loading}
        />
      ),
    },
    {
      label: 'Template Placement',
      count: queueData.templatePlacement.count,
      content: (
        <QueuesTable
          data={queueData.templatePlacement.data}
          loading={queueData.templatePlacement.loading}
          queueType="template-placement"
        />
      ),
    },
    {
      label: 'Ready For Artwork',
      count: queueData.readyForArtwork.count,
      content: (
        <QueuesTable
          data={queueData.readyForArtwork.data}
          loading={queueData.readyForArtwork.loading}
          queueType="ready-for-artwork"
        />
      ),
    },
    {
      label: 'Revision Artwork',
      count: queueData.revisionArtwork.count,
      content: (
        <QueuesTable
          data={queueData.revisionArtwork.data}
          loading={queueData.revisionArtwork.loading}
          queueType="revision-artwork"
        />
      ),
    },
  ];

  function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
      <div
        role="tabpanel"
        hidden={value !== index}
        id={`simple-tabpanel-${index}`}
        aria-labelledby={`simple-tab-${index}`}
        {...other}
      >
        {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
      </div>
    );
  }

  function a11yProps(index: number) {
    return {
      id: `simple-tab-${index}`,
      'aria-controls': `simple-tabpanel-${index}`,
    };
  }

  const [value, setValue] = useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs 
          value={value} 
          onChange={handleChange} 
          aria-label="queue tabs"
          variant="scrollable"
          scrollButtons="auto"
        >
          {tabs.map((tab, index) => (
            <Tab
              key={index}
              label={tab.label}
              {...a11yProps(index)}
            />
          ))}
        </Tabs>
      </Box>
      {tabs.map((tab, index) => (
        <CustomTabPanel key={index} value={value} index={index}>
          {tab.content}
        </CustomTabPanel>
      ))}
    </>
  );
};

export default QueuesTabs;
