'use client';
import { <PERSON>, Card, CardContent, Divider, Typography } from '@mui/material';
import React from 'react';
import QueuesTabs from './components/QueuesTabs';

const QueuesWrapper = () => {
  return (
    <Card>
      <CardContent>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 4,
          }}
        >
          <Typography variant="h5">Queues</Typography>
        </Box>

        <Divider sx={{ mb: 4 }} />

        <QueuesTabs />
      </CardContent>
    </Card>
  );
};

export default QueuesWrapper;
