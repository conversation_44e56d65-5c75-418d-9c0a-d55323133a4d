'use client';
import React, { useEffect, useState, useMemo } from 'react';
import { Tab, Tabs, Typography, Box, Chip, Button } from '@mui/material';
import Grid from '@mui/material/Grid2';
import { useSettings } from '@core/hooks/useSettings';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/redux-store';
import { generateDynamicColumns } from '../ProductSkuListTable';
import DataTable from '@/components/Datatable';
import { fetchOrders, filterOrder, normalizeFilterValues } from '@/actions/orders';
import { setOrders, setOrdersFilters } from '@/redux-store/stores/orders.store';
import { useRouter } from 'next/navigation';
import StatusChip from '@/components/StatusChip';
import { toast } from 'react-toastify';
import SyncIcon from '@mui/icons-material/Sync';
import apiClient from '@/utils/axios';
import { Add, PlusOne } from '@mui/icons-material';
import { useAbility } from '@/libs/casl/AbilityContext';
import { Actions, ActionsTarget } from '@/libs/casl/ability';

const OrdersWrapper = ({ page = 0, limit = 25, data }: { page?: any; limit: any; data: any }) => {
  const router = useRouter();
  const { updatePageSettings } = useSettings();
  const dispatch = useDispatch<AppDispatch>();
  const ability = useAbility();
  const { orders, filters } = useSelector((state: RootState) => state.orders);
  const ordersTableField = useSelector((state: RootState) => state.common.ordersTableConfig);
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [filtersApplied, setFiltersApplied] = useState(false);

  useEffect(() => {
    const cleanup = updatePageSettings({ skin: 'default' });
    if (data) dispatch(setOrders(data));
    const url = new URL(window.location.href);
    url.searchParams.set('page', page.toString());
    url.searchParams.set('limit', limit.toString());
    window.history.replaceState({}, '', url.toString());

    return cleanup;
  }, [data, dispatch, updatePageSettings, page, limit]);

  // Tab change handler
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleRowClick = (row: any, e?: React.MouseEvent) => {
    if (ability?.can(Actions.EditOrderDetialPage, ActionsTarget.ORDERS)) {
      const path = `/ordermanagement/orders/view/${row.id}`;
      if (e && (e.ctrlKey || e.metaKey)) {
        window.open(path, '_blank', 'noopener,noreferrer');
      } else {
        router.push(path);
      }
    }
  };

  // Create a custom navigation handler function
  const handleNavigation = (e: React.MouseEvent, path: string) => {
    // If Ctrl or Cmd key is pressed, open in new tab
    if (e.ctrlKey || e.metaKey) {
      e.preventDefault(); // Prevent default behavior
      e.stopPropagation(); // Stop event propagation
      window.open(path, '_blank', 'noopener,noreferrer');
    } else {
      // Normal navigation in current tab
      router.push(path);
    }
  };

  // Process dynamic columns
  const processedDynamicColumns = useMemo(() => {
    return ordersTableField?.fields
      ? generateDynamicColumns(
          ordersTableField.fields.map(field => {
            if (field.key === 'shopifyOrderNumber') {
              // Create a custom cell renderer for shopifyOrderNumber
              return {
                ...field,
                cell: (info: { getValue: () => any; row: any }) => {
                  const value = info.getValue();
                  const canNavigate = ability?.can(
                    Actions.EditOrderDetialPage,
                    ActionsTarget.ORDERS,
                  );

                  return (
                    <Typography
                      sx={{
                        cursor: canNavigate ? 'pointer' : 'default',
                        color: 'primary.main',
                        '&:hover': {
                          textDecoration: canNavigate ? 'underline' : 'none',
                        },
                      }}
                      onClick={() => {
                        if (canNavigate) {
                          router.push(`/ordermanagement/orders/view/${info.row.original.id}`);
                        }
                      }}
                    >
                      {value ?? '-'}
                    </Typography>
                  );
                },
              };
            }

            if (field.key === 'customerFirstName' || field.key === 'customerLastName') {
              return { ...field, hidden: true };
            }

            if (field.key === 'orderDate') {
              return {
                ...field,
                cell: (info: { getValue: () => any }) => {
                  const date = info.getValue();
                  if (!date) return '-';

                  try {
                    const dateObj = new Date(date);
                    const year = dateObj.getFullYear();
                    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
                    const day = String(dateObj.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                  } catch (error) {
                    return date;
                  }
                },
              };
            }

            return field;
          }),
          router,
        )
      : [];
  }, [ordersTableField?.fields, router]);

  const tableColumns = useMemo(
    () => [
      {
        accessorKey: 'shopifyOrderNumber',
        header: 'Order Number',
        enableSorting: false,
        cell: (info: { getValue: () => any; row: any }) => {
          const value = info.getValue();
          const canNavigate = ability?.can(Actions.EditOrderDetialPage, ActionsTarget.ORDERS);
          const path = `/ordermanagement/orders/view/${info.row.original.id}`;

          return (
            <Typography
              sx={{
                cursor: canNavigate ? 'pointer' : 'default',
                color: 'primary.main',
                '&:hover': {
                  textDecoration: canNavigate ? 'underline' : 'none',
                },
              }}
              onClick={e => {
                e.stopPropagation();
                if (canNavigate) {
                  handleNavigation(e, path);
                }
              }}
            >
              {value ?? '-'}
            </Typography>
          );
        },
      },
      {
        accessorFn: (row: any) =>
          `${row.customerFirstName || ''} ${row.customerLastName || ''}`.trim(),
        id: 'customerName',
        header: 'Customer Name',
        enableSorting: false,
        cell: (info: { getValue: () => any }) => (
          <Typography noWrap sx={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis' }}>
            {info.getValue() || '-'}
          </Typography>
        ),
      },
      {
        accessorKey: 'orderDate',
        header: 'Order Date',
        enableSorting: false,
        cell: (info: { getValue: () => any }) => {
          const date = info.getValue();
          if (!date) return '-';
          try {
            const dateObj = new Date(date);
            const year = dateObj.getFullYear();
            const month = String(dateObj.getMonth() + 1).padStart(2, '0');
            const day = String(dateObj.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
          } catch (error) {
            return '-';
          }
        },
      },
      ...processedDynamicColumns.filter(col => {
        const id = col.id?.toString() || '';
        const accessorKey = (col as any).accessorKey?.toString() || '';
        const header = (col as any).header?.toString() || '';

        return (
          id !== 'customerFirstName' &&
          accessorKey !== 'customerFirstName' &&
          id !== 'customerLastName' &&
          accessorKey !== 'customerLastName' &&
          id !== 'orderStatus' &&
          accessorKey !== 'orderStatus' &&
          id !== 'Priority' &&
          accessorKey !== 'Priority' &&
          !id.endsWith('_priorities') &&
          accessorKey !== 'priorities' &&
          id !== 'orderDate' &&
          accessorKey !== 'orderDate' &&
          id !== 'shopifyOrderNumber' &&
          accessorKey !== 'shopifyOrderNumber' &&
          header !== 'ORDER #' &&
          header !== 'Order #'
        );
      }),
      {
        accessorFn: (row: any) => {
          if (!row.statusUpdatedAt) return null;
          const statusDate = new Date(row.statusUpdatedAt);
          const currentDate = new Date();
          const statusDateOnly = new Date(statusDate.setHours(0, 0, 0, 0));
          const currentDateOnly = new Date(currentDate.setHours(0, 0, 0, 0));
          const diffTime = Math.abs(currentDateOnly.getTime() - statusDateOnly.getTime());
          const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
          return diffDays;
        },
        id: 'daysSinceStatusUpdate',
        header: 'Days Since Status Update',
        enableSorting: false,
        cell: (info: { getValue: () => any }) => {
          const days = info.getValue();
          if (days === null) return <Typography>-</Typography>;
          let color = 'text.primary';
          if (days > 7) color = 'warning.main';
          if (days > 14) color = 'error.main';

          return <Typography color={color}>{days} days</Typography>;
        },
      },
      {
        accessorKey: 'priorities',
        header: 'Priorities',
        enableSorting: false,
        meta: {
          filterType: 'multi_select',
        },
        cell: (info: { getValue: () => any }) => {
          const priorities = info.getValue();
          return Array.isArray(priorities) && priorities.length > 0 ? (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {priorities.map((priority: string, index: number) => (
                <Chip
                  key={`${priority}-${index}`}
                  label={priority}
                  size="small"
                  sx={{
                    backgroundColor: priority.toUpperCase().includes('HOLIDAY')
                      ? '#ffcccc'
                      : priority.toUpperCase().includes('RUSH')
                        ? '#ffe0b2'
                        : '#ccc',
                    color: '#000',
                  }}
                />
              ))}
            </Box>
          ) : (
            <Typography variant="body2">No priorities</Typography>
          );
        },
      },
      {
        accessorKey: 'orderStatus',
        header: 'Status',
        enableSorting: false,
        cell: (info: { getValue: () => any }) => (
          <StatusChip status={info.getValue()} variant="order" size="small" />
        ),
      },
    ],
    [processedDynamicColumns],
  );

  // Get current tab data
  const currentTabData = useMemo(() => {
    return activeTab === 0 ? orders.unflagged?.data || [] : orders.flagged?.data || [];
  }, [activeTab, orders]);

  const currentTabCount = useMemo(() => {
    return activeTab === 0 ? orders.unflagged?.count || 0 : orders.flagged?.count || 0;
  }, [activeTab, orders.unflagged?.count, orders.flagged?.count]);

  // Filter handler
  const handleApplyFilters = async (normalizedFilters: any) => {
    try {
      setLoading(true);
      const isReset = !normalizedFilters || normalizedFilters.length === 0;

      let unflaggedResult: any;
      let flaggedResult: any;

      if (isReset) {
        setFiltersApplied(false);
        dispatch(setOrdersFilters([]));
        const unflaggedPromise = fetchOrders({
          page: Number(page),
          limit: Number(limit),
          fq: 'flagged:eq:false',
        });

        const flaggedPromise = fetchOrders({
          page: Number(page),
          limit: Number(limit),
          fq: 'flagged:eq:true',
        });

        [unflaggedResult, flaggedResult] = await Promise.all([unflaggedPromise, flaggedPromise]);
        toast.success('Filters reset successfully');
      } else {
        setFiltersApplied(true);
        const processedFilters = normalizedFilters
          .map((group: any[]) =>
            group
              .map((filter: any) => {
                if (filter.attribute === 'orderDate') {
                  return {
                    ...filter,
                    operator: 'between',
                    value:
                      filter.value && typeof filter.value === 'object'
                        ? { start: filter.value.start, end: filter.value.end }
                        : { start: '', end: '' },
                  };
                }
                return filter;
              })
              .filter((filter: any) => {
                if (!filter.attribute || !filter.operator) return false;
                if (filter.attribute === 'orderDate') {
                  return (
                    filter.value &&
                    typeof filter.value === 'object' &&
                    filter.value.start &&
                    filter.value.end
                  );
                }
                return filter.value !== undefined && filter.value !== null && filter.value !== '';
              }),
          )
          .filter((group: any[]) => group.length > 0);

        const normalizedProcessedFilters = normalizeFilterValues(processedFilters);

        const unflaggedPayload = {
          filters: normalizedProcessedFilters.map((group: any) => [
            ...group,
            { attribute: 'flagged', operator: 'eq', value: false },
          ]),
          page: Math.max(1, Number(page)),
          limit: Number(limit),
        };

        const flaggedPayload = {
          filters: normalizedProcessedFilters.map((group: any) => [
            ...group,
            { attribute: 'flagged', operator: 'eq', value: true },
          ]),
          page: Math.max(1, Number(page)),
          limit: Number(limit),
        };

        [unflaggedResult, flaggedResult] = await Promise.all([
          filterOrder(unflaggedPayload),
          filterOrder(flaggedPayload),
        ]);

        dispatch(setOrdersFilters(normalizedFilters));
        toast.success('Filters applied successfully');
      }

      dispatch(
        setOrders({
          unflagged: {
            data: Array.isArray(unflaggedResult.data) ? [...unflaggedResult.data] : [],
            count: unflaggedResult.count || 0,
          },
          flagged: {
            data: Array.isArray(flaggedResult.data) ? [...flaggedResult.data] : [],
            count: flaggedResult.count || 0,
          },
        }),
      );

      setLoading(false);
      return activeTab === 0 ? unflaggedResult : flaggedResult;
    } catch (error) {
      setLoading(false);
      toast.error('Filter operation failed');
      return null;
    }
  };

  const handleSyncOrders = async () => {
    try {
      setLoading(true);
      const response = await apiClient.post('/orders/resync_orders');
      const { message } = response.data;
      toast.success(message || 'Order sync initiated successfully. This may take a few minutes.');
    } catch (error) {
      toast.error('Failed to sync orders. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  return (
    <>
      <Grid container spacing={3}>
        <Grid size={{ xs: 12 }} className="flex justify-between items-center mb-2">
          <Typography variant="h4">Orders</Typography>
          <div className="flex justify-center items-center gap-2">
            {activeTab === 0 && (
              <Button
                variant="contained"
                color="primary"
                startIcon={<SyncIcon />}
                onClick={handleSyncOrders}
                disabled={loading}
              >
                Sync Orders
              </Button>
            )}
            <Button
              variant="contained"
              color="primary"
              startIcon={<Add />}
              onClick={() => router.push('./orders/add')}
              disabled={loading}
            >
              Add Order
            </Button>
          </div>
        </Grid>
        <Grid size={{ xs: 12 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            aria-label="order tabs"
            variant="scrollable"
            scrollButtons="auto"
            allowScrollButtonsMobile
            sx={{
              '.MuiTabs-scrollButtons': {
                opacity: 1,
                '&.Mui-disabled': { opacity: 0.3 },
              },
            }}
          >
            <Tab label="Orders" value={0} />
            <Tab label="Flagged Orders" value={1} />
          </Tabs>

          <DataTable
            key="orders-table-persistent"
            filterFields={ordersTableField.filterDataFields}
            title="Filters Orders"
            storeName="orders"
            onApplyFilters={handleApplyFilters}
            filterCount={filters.flat().length}
            totalCount={currentTabCount}
            enableFilters={true}
            columns={tableColumns}
            data={currentTabData}
            onRowClick={row => handleRowClick(row, window.event as unknown as React.MouseEvent)}
            loading={loading}
            page={Math.max(0, Number(page) - 1)}
            limit={Number(limit)}
            endpoint="ordermanagement/orders"
            onLimitChange={(newLimit: number) => {
              const url = new URL(window.location.href);
              url.searchParams.set('limit', newLimit.toString());
              url.searchParams.set('page', '1');
              window.location.href = url.toString();
            }}
          />
        </Grid>
      </Grid>
    </>
  );
};

export default OrdersWrapper;
