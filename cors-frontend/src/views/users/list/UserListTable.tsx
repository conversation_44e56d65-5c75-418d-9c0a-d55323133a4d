"use client";
import { useEffect, useState, useMemo } from "react";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import Chip from "@mui/material/Chip";
import IconButton from "@mui/material/IconButton";
import type { TextFieldProps } from "@mui/material/TextField";
import { rankItem } from "@tanstack/match-sorter-utils";
import { createColumnHelper } from "@tanstack/react-table";
import type { ColumnDef, FilterFn, SortingState } from "@tanstack/react-table";
import type { RankingInfo } from "@tanstack/match-sorter-utils";
import CustomAvatar from "@core/components/mui/Avatar";
import { getInitials } from "@/utils/getInitials";
import { UsersType } from "@/types/userTypes";
import DataTable from "@/components/Datatable";
import { useSession } from "next-auth/react";
import { toast } from "react-toastify";
import Button from "@mui/material/Button";
import { useRouter} from "next/navigation";
import apiClient from "@/utils/axios";
import { RoleProtected } from "@/components/ProtectedRoleWrapper";
import { Actions, ActionsTarget } from "@/libs/casl/ability";
import ConfirmationDialog from "@/components/ConfirmationDialog";

declare module "@tanstack/table-core" {
  interface FilterFns {
    fuzzy: FilterFn<unknown>;
  }
  interface FilterMeta {
    itemRank: RankingInfo;
  }
}


type UsersTypeWithAction = UsersType & {
  action?: string;
};

const fuzzyFilter: FilterFn<any> = (row, columnId, value, addMeta) => {
  const itemRank = rankItem(row.getValue(columnId), value);
  addMeta({
    itemRank,
  });
  return itemRank.passed;
};

const DebouncedInput = ({
  value: initialValue,
  onChange,
  debounce = 500,
  ...props
}: {
  value: string | number;
  onChange: (value: string | number) => void;
  debounce?: number;
} & Omit<TextFieldProps, "onChange">) => {
  const [value, setValue] = useState(initialValue);

  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  useEffect(() => {
    const timeout = setTimeout(() => {
      onChange(value);
    }, debounce);

    return () => clearTimeout(timeout);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  return (
    <TextField
      {...props}
      value={value}
      onChange={(e) => setValue(e.target.value)}
      size="small"
    />
  );
};

const columnHelper = createColumnHelper<UsersTypeWithAction>();

const UserListTable = ({
  tableData,
  count,
}: {
  tableData?: UsersType[];
  count: number;
}) => {
  const { data: session } = useSession();
  const router = useRouter();
  const [data, setData] = useState<UsersType[]>([]);
  const [openStatusModal, setOpenStatusModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UsersType | null>(null);
  const [loading, setLoading] = useState(false);
  const [openEditModal, setOpenEditModal] = useState(false);
  const [editUserData, setEditUserData] = useState<UsersType | null>(null);
  const [editFormData, setEditFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
  });

  // Add navigation handler for Ctrl/Cmd+click
  const handleNavigation = (e: React.MouseEvent, path: string) => {
    // If Ctrl or Cmd key is pressed, open in new tab
    if (e.ctrlKey || e.metaKey) {
      window.open(path, "_blank");
    } else {
      // Normal navigation
      router.push(path);
    }
  };


  useEffect(() => {
    setData(tableData ?? []);
  }, [tableData]);

  const handleStatusChange = (user: UsersType) => {
    setSelectedUser(user);
    setOpenStatusModal(true);
  };

  const handleConfirmStatusChange = async () => {
    if (!selectedUser || !session?.user?.token) return;

    setLoading(true);

    try {
      const newStatus = !selectedUser.isActive;

      await apiClient.put(`/users/${selectedUser.id}`, {
        isActive: newStatus,
        roleIds: selectedUser.roles
          ? selectedUser.roles.map((role) => role.id)
          : [],
      });

      const updatedData = data.map((user) => {
        if (user.id === selectedUser.id) {
          return { ...user, isActive: newStatus };
        }
        return user;
      });

      setData(updatedData);

      toast.success(
        `User ${newStatus ? "activated" : "deactivated"} successfully`
      );
      setOpenStatusModal(false);
    } catch (error) {
      console.error("Failed to update user status:", error);
      toast.error("Failed to update user status. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setEditFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const nextPage = ({
    pageNo,
    pageSize,
  }: {
    pageNo: string;
    pageSize: number;
  }) => {};

  const handleEditSubmit = async () => {
    if (!editUserData || !session?.user?.token) return;

    setLoading(true);

    try {
      await apiClient.put(`/users/${editUserData.id}`, {
        ...editFormData,
      });

      const updatedData = data.map((user) => {
        if (user.id === editUserData.id) {
          return {
            ...user,
            firstName: editFormData.firstName,
            lastName: editFormData.lastName,
            email: editFormData.email,
            fullName: `${editFormData.firstName} ${editFormData.lastName}`,
          };
        }
        return user;
      });

      setData(updatedData);
      toast.success("User updated successfully");
      setOpenEditModal(false);
    } catch (error) {
      console.error("Failed to update user:", error);
      toast.error("Failed to update user. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const columns = useMemo<ColumnDef<UsersTypeWithAction, any>[]>(
    () => [
      columnHelper.accessor("email", {
        header: "Email",
        enableColumnFilter: true,
        cell: ({ row }) => <Typography>{row.original.email}</Typography>,
      }),
      columnHelper.accessor("firstName", {
        header: "First Name",
        enableColumnFilter: true,
        enableSorting: true,
        cell: ({ row }) => (
          <Typography className="font-medium" color="text.primary">
            {row.original.firstName}
          </Typography>
        ),
      }),
      columnHelper.accessor("lastName", {
        header: "Last Name",
        enableColumnFilter: true,
        enableSorting: true,
        cell: ({ row }) => (
          <Typography className="font-medium" color="text.primary">
            {row.original.lastName}
          </Typography>
        ),
      }),
      columnHelper.accessor("roles", {
        header: "Role",
        enableColumnFilter: true,
        enableSorting: true,
        meta : {optKey : 'roles.name'},
        cell: ({ row }) => (
          <Typography>
            {Array.isArray(row.original.roles) && row.original.roles.length > 0
              ? row.original.roles.map((role) => role.name).join(", ")
              : "No Role Assigned"}
          </Typography>
        ),
      }),
      columnHelper.accessor("isActive", {
        header: "Status",
        enableColumnFilter: true,
        meta: {
          filterType: "select",
          isBoolean: true,
          booleanOptions: { Active: true, Inactive: false },
        },
        enableSorting: true,
        cell: ({ row }) => {
          const isActive = row.original.isActive;
          const statusText = isActive ? "Active" : "Inactive";
          const statusColor = isActive ? "success" : "error";

          return (
            <div className="flex items-center gap-3">
              <Chip
                variant="tonal"
                label={statusText}
                size="small"
                color={statusColor}
                className="capitalize"
              />
            </div>
          );
        },
      }),
      columnHelper.accessor("action", {
        header: "Action",
        enableColumnFilter: false,
        cell: ({ row }) => {
          const target = row.original.roles?.find(
            (role) => role.name === "Owner"
          )
            ? ActionsTarget.ALL
            : ActionsTarget.UserManagment;
          return (
            <div className="flex items-center">
              <RoleProtected
                action={Actions.ViewUserDetail}
                actionTarget={target}>
                <IconButton
                  onClick={(e) => {
                    handleNavigation(e, `/users/view/${row.original.id}`);
                  }}>
                  <i className="ri-eye-line text-textSecondary" />
                </IconButton>
              </RoleProtected>
              <RoleProtected action={Actions.EditUser} actionTarget={target}>
                <IconButton
                  onClick={(e) => {
                    handleNavigation(e, `/users/edit/${row.original.id}`);
                  }}>
                  <i className="ri-edit-line text-textSecondary" />
                </IconButton>
              </RoleProtected>

              <RoleProtected
                action={
                  row.original.isActive
                    ? Actions.DeactivateUser
                    : Actions.ActivateUser
                }
                actionTarget={target}>
                <Button
                  variant="outlined"
                  size="small"
                  color={row.original.isActive ? "error" : "success"}
                  onClick={() => handleStatusChange(row.original)}
                  sx={{ ml: 1 }}>
                  {row.original.isActive ? "Deactivate" : "Activate"}
                </Button>
              </RoleProtected>
            </div>
          );
        },
        enableSorting: false,
      }),
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [data]
  );

  const getAvatar = (params: Pick<UsersType, "avatar" | "fullName">) => {
    const { avatar, fullName } = params;

    if (avatar) {
      return <CustomAvatar src={avatar} skin="light" size={34} />;
    } else {
      return (
        <CustomAvatar skin="light" size={34}>
          {getInitials(fullName as string)}
        </CustomAvatar>
      );
    }
  };

  return (
    <>
      <DataTable
        data={data}
        columns={columns}
        loading={false}
        endpoint="users"
        totalCount={count}
        globalSearch
      />
      <ConfirmationDialog
        open={openStatusModal}
        title={`${selectedUser?.isActive ? "Deactivate" : "Activate"} User`}
        message={
          selectedUser && (
            <>
              Are you sure you want to{" "}
              {selectedUser.isActive ? "deactivate" : "activate"} this user?
              {selectedUser.isActive
                ? " This will prevent the user from accessing the system."
                : " This will allow the user to access the system."}
            </>
          )
        }
        confirmLabel="Confirm"
        confirmColor={selectedUser?.isActive ? "error" : "success"}
        loading={loading}
        onConfirm={handleConfirmStatusChange}
        onCancel={() => setOpenStatusModal(false)}
      />
      <ConfirmationDialog
        open={openEditModal}
        title="Edit User"
        message={
          <div className="mt-4 space-y-4">
            <TextField
              fullWidth
              label="First Name"
              name="firstName"
              value={editFormData.firstName}
              onChange={handleEditInputChange}
            />
            <TextField
              fullWidth
              label="Last Name"
              name="lastName"
              value={editFormData.lastName}
              onChange={handleEditInputChange}
            />
            <TextField
              fullWidth
              label="Email"
              name="email"
              value={editFormData.email}
              onChange={handleEditInputChange}
            />
          </div>
        }
        confirmLabel="Save Changes"
        confirmColor="primary"
        loading={loading}
        onConfirm={handleEditSubmit}
        onCancel={() => setOpenEditModal(false)}
      />
    </>
  );
};

export default UserListTable;
