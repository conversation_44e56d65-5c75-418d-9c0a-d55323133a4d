import { AddonType, OrderItemType, UniqueCharacteristic } from '@/types/manual-order.type';
import { AddonData } from '@/types/manual-order.type';
import { OrderFormSchemaType } from '@/views/order-management/orders/add/validations/manual-order.validation';

export const getPetsCountFromSKU = (sku: string | undefined) => {
  if (!sku) {
    return 1;
  }
  try {
    const petCountRegex = /(\d+)\s*pets?/i;
    const petCountMatch = sku.match(petCountRegex);
    return petCountMatch ? parseInt(petCountMatch[1]) : 1;
  } catch (error) {
    console.error('Error parsing SKU:', error);
    return 1;
  }
};
export const getCustomerPayload = (data: OrderFormSchemaType) => {
  const {
    firstName,
    lastName,
    customerEmail,
    country,
    phoneNumber,
    shippingAddress,
    city,
    state,
    zipCode,
  } = data;

  const addressInfo = {
    first_name: firstName,
    last_name: lastName,
    address1: shippingAddress,
    phone: phoneNumber,
    city: city,
    zip: zipCode,
    state: state,
    country: country?.label,
    name: `${firstName} ${lastName}`,
    country_code: country?.code,
  };

  return {
    billing_address: addressInfo,
    customer: {
      email: customerEmail,
      first_name: firstName,
      last_name: lastName,
      phone: phoneNumber,
      currency: 'USD',
      metafields: [],
      default_address: {
        ...addressInfo,
        default: true,
      },
    },
    shipping_address: addressInfo,
  };
};
//Payload Building
export const formatOrderItemsWithAddOn = (
  data: OrderFormSchemaType,
  allIdentifiers: AddonType[],
) => {
  return data?.orderItems?.map((orderItem: OrderItemType) => {
    if (!orderItem?.product?.metadata?.product_customizer) {
      const extrasProducts =
        JSON.parse(orderItem?.product?.metadata?.extras || '{}')?.extras_products || [];

      const selectedIdentifiers = extrasProducts.reduce((acc: any[], extraProduct: AddonType) => {
        const matchingIdentifier = allIdentifiers.find((identifier: AddonType) => {
          if (
            orderItem?.sku?.addonData?.find(
              (data: AddonData) => data?.variantId == String(identifier?.id),
            )?.addonLevel === 'line_item'
          ) {
            return (
              identifier.id === extraProduct.id &&
              identifier?.timestampkey === orderItem?.product?.timestampkey
            );
          } else {
            return identifier.id === extraProduct.id;
          }
        });

        if (matchingIdentifier) {
          acc.push(matchingIdentifier);
        }
        return acc;
      }, []);

      return {
        ...orderItem,
        selectedIdentifiers,
      };
    }
    return orderItem;
  });
};
export const finalLineItemsPayload = (
  data: any,
  allIdentifiers: AddonType[],
  allIdentifiersPlush: any[],
  allIdentifiersPdp: any[],
) => {
  const productLineItems = data?.map((item: any) => {
    const isCustomizer = item?.product?.metadata?.product_customizer;
    const isPdp = item?.product?.metadata?.pdp_customizer;

    const baseProperties = [
      ...(item?.selectedIdentifiers?.map((identifier: AddonType) => ({
        name: identifier?.name?.trim()?.toLowerCase()?.replace(/\s+/g, '_'),
        value: identifier?.id,
      })) || []),
      ...(isCustomizer || isPdp
        ? [
            {
              name: '_original_unique_key',
              value: item?.product?.timestampkey,
            },
          ]
        : []),
      ...(isPdp ? [] : [...item?.sku?.shopifyNativeVariant, ...item?.sku?.shopifyCustomVariant]),
    ];

    const customizerProperties = isCustomizer
      ? [
          { name: '_pet_type', value: item?._pet_type || '' },
          { name: '_pet_name', value: item?._pet_name || '' },
          { name: '_pet_breed_specie', value: item?._pet_breed_specie || '' },
          { name: '_pet_age', value: item?._pet_age || '' },
          { name: '_pet_left_eye', value: item?.left_eye_color || '' },
          { name: '_pet_right_eye', value: item?.left_eye_color || item?.right_eye_color },
          ...(item?.left_ear_position
            ? [{ name: 'left_ear_position', value: item.left_ear_position }]
            : []),
          ...(item?.right_ear_position
            ? [{ name: 'right_ear_position', value: item.right_ear_position }]
            : []),

          ...(item?.productOptions?.map((option: Record<string, string>) => ({
            name: option?.name?.trim()?.toLowerCase()?.replace(/\s+/g, '_'),
            value: option?.value,
          })) || []),

          ...(item?.uniqueCharacteristics?.map((char: UniqueCharacteristic, index: number) => ({
            name: `_pet_characteristic_${index + 1}`,
            value: char.description || '',
          })) || []),
          ...(item?.uniqueCharacteristics?.map((char: UniqueCharacteristic, index: number) => ({
            name: `characteristic_${index + 1}`,
            value: char.image[0] || '',
          })) || []),
          ...(item?.petImagesCustomizer?.map((image: string, index: number) => ({
            name: `_image_url_${index + 1}`,
            value: image,
          })) || []),
          ...(item?.otherOptions?.map((option: Record<string, string>) => ({
            name: option?.name?.trim()?.toLowerCase()?.replace(/\s+/g, '_'),
            value: option?.value,
          })) || []),
          ...(item?.customizer_questions?.map((question: Record<string, string>) => ({
            name: question?.label?.trim()?.toLowerCase()?.replace(/\s+/g, '_'),
            value: question?.value,
          })) || []),
        ]
      : isPdp
        ? [
            ...(item?.pdp_swatches?.map(
              (data: { label: string; value: string }, index: number) => ({
                name: data?.label?.toLowerCase()?.replace(/\s+/g, '_'),
                value: data?.value,
              }),
            ) || []),

            ...(item?.pdp_frame_options?.length > 0
              ? [
                  {
                    name: `_option_${item?.pdp_frame_options?.map((option: any) => option?.label).join(' and ')}`,
                    value: item?.pdp_frame_options
                      ?.map(
                        (option: any) =>
                          `${option?.selectedOption} ${option?.selectedSubOption || ''}`,
                      )
                      .join(' '),
                  },
                ]
              : []),
            ...(item?.pdp_pet_images_data?.map((data: { pet_images: string[] }, index: number) => ({
              name: `_image_url_${index + 1}`,
              value: data?.pet_images[0],
            })) || []),
            ...(item?.pdp_pet_images_data?.map((data: { pet_name: string }, index: number) => ({
              name: `Pet Name ${index + 1}`,
              value: data?.pet_name,
            })) || []),
            ...(item?.pdp_pet_images_data?.map(
              (data: { back_engraving: string }, index: number) => ({
                name: `Back Engraving ${index + 1}`,
                value: data?.back_engraving,
              }),
            ) || []),
            ...(item?.pdp_pet_images_data?.map(
              (data: { front_engraving: string }, index: number) => ({
                name: `Front Engraving ${index + 1}`,
                value: data?.front_engraving,
              }),
            ) || []),
          ]
        : item?.petImages?.map((image: string, index: number) => ({
            name: `_image_url_${index + 1}`,
            value: image,
          }));

    return {
      quantity: item?.quantity,
      sku: item?.sku?.sku,
      properties: [...baseProperties, ...customizerProperties],
    };
  });

  const addOnLineItems = allIdentifiers?.map((item: AddonType) => ({
    quantity: item?.quantity || 1,
    properties: [
      {
        name: item?.name?.trim()?.toLowerCase()?.replace(/\s+/g, '_'),
        value: item?.id,
      },
      {
        name: 'is_addon',
        value: true,
      },
    ],
  }));

  const addOnLineItemsPlush = allIdentifiersPlush?.map(
    (item: {
      name: string;
      variant: string;
      _unique_key: string;
      quantity?: number;
      pet_name: string;
    }) => ({
      quantity: item?.quantity || 1,
      properties: [
        {
          name: item?.name?.trim()?.toLowerCase()?.replace(/\s+/g, '_'),
          value: item?.variant,
        },
        {
          name: '_unique_key',
          value: item?._unique_key,
        },
        {
          name: 'is_addon',
          value: true,
        },
        ...(item?.pet_name
          ? [
              {
                name: 'pet_name',
                value: item?.pet_name,
              },
            ]
          : []),
      ],
    }),
  );

  const addOnLineItemsPdp = allIdentifiersPdp?.map(
    (item: { name: string; id: string; _unique_key: string; quantity?: number }) => ({
      quantity: item?.quantity || 1,
      properties: [
        {
          name: '_unique_key',
          value: item?._unique_key,
        },
        {
          name: 'is_addon',
          value: true,
        },
        {
          name: item?.name?.trim()?.toLowerCase()?.replace(/\s+/g, '_'),
          value: item?.id,
        },
      ],
    }),
  );
  const productLineItemsPdpEngraving = data
    ?.map((item: any) => {
      if (item?.pdp_pet_images_data?.length > 0) {
        const engravingData = item?.pdp_pet_images_data
          ?.map((data: any) => {
            if (data?.back_engraving) {
              return {
                name: '_unique_key',
                value: item?.product?.timestampkey,
              };
            }
          })
          .filter(Boolean);
        console.log(engravingData, 'engravingData');
        if (engravingData?.length) {
          return {
            quantity: engravingData?.length || 1,
            properties: [
              {
                name: '_unique_key',
                value: item?.product?.timestampkey,
              },
              {
                name: 'is_addon',
                value: true,
              },
              {
                name: 'back_engraving',
                value: true,
              },
            ],
          };
        }
      }
    })
    .filter(Boolean);

  return [
    ...productLineItems,
    ...addOnLineItems,
    ...addOnLineItemsPlush,
    ...addOnLineItemsPdp,
    ...productLineItemsPdpEngraving,
  ];
};
