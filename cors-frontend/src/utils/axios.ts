import { authOptions } from '@/libs/auth';
import axios from 'axios';
import { getServerSession } from 'next-auth/next';
import { getSession } from 'next-auth/react';


const API_URL = process.env.NEXT_PUBLIC_API_URL;

const apiClient = axios.create({
  baseURL: API_URL,
  withCredentials: false,
});

let isRefreshing = false;
let failedQueue: any[] = [];

const logout: () => void = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('auth-token');
    localStorage.removeItem('refresh-token');
    document.cookie = 'auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    window.location.href = '/login';
  }
};

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

const refreshToken = async (refreshToken: string) => {
  try {
    const response = await apiClient.post('/auth/refresh', {
      refresh: refreshToken,
    });

    if (response.data.access_token && typeof window !== 'undefined') {

      localStorage.setItem('auth-token', response.data.access_token);
      if (response.data.refresh_token) {
        localStorage.setItem('refresh-token', response.data.refresh_token);
      }
      // document.dispatchEvent(new Event('visibilitychange'));
    }

    return response?.data;
  } catch (error) {
    console.error('Token refresh error:', error);
    throw error;
  }
};


apiClient.interceptors.request.use(
  async (config) => {
    if (typeof window !== 'undefined') {
      const session = await getSession();
      if (session?.user?.token) {
        config.headers['Authorization'] = `Bearer ${session.user.token}`;
      }
    } else {
      const session = await getServerSession(authOptions);
      if (session?.user?.token) {
        config.headers['Authorization'] = `Bearer ${session.user.token}`;
      }
    }
    config.headers['ngrok-skip-browser-warning'] = `69420`;

    return config;
  },
  (error) => Promise.reject(error)
);

apiClient.interceptors.response.use(
  (res) => res,
  async (err) => {
    const originalRequest = err.config;
    if (typeof window === 'undefined') {
      const session = await getSession();;

      // return Promise.reject(err);
    }
    return Promise.reject(err);
  }
);

export default apiClient;
