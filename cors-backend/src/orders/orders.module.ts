import { Modu<PERSON> } from '@nestjs/common';
import { OrdersService } from './orders.service';
import { OrdersController } from './orders.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Order } from './entities/order.entity';
import { LineItem } from './entities/line-item.entity';
import { Attachment } from '../attachments/entities/attachment.entity';
import { AttachmentModule } from '../attachments/attachment.module';
import { ProductSku } from '../product-sku/entities/product-sku.entity';
import { User } from 'src/users/entities/user.entity';
import { BullModule } from '@nestjs/bullmq';
@Module({
  imports: [
    TypeOrmModule.forFeature([Order, LineItem, Attachment, ProductSku, User]),
    AttachmentModule,
    BullModule.registerQueue({
      name: 'order-sync',
    }),
  ],
  controllers: [OrdersController],
  providers: [OrdersService],
})
export class OrdersModule { }
