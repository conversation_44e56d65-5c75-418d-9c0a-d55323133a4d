import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { User } from './entities/user.entity';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { UpdateUserDto } from './dto/update-user.dto';
import { JwtGuard } from 'src/auth/guards/jwt-auth.guard';
import { PermissionsGuard } from 'src/permissions/permissions.guards';
import { CurrentUser } from 'src/utils/current-user.decorator';

@UseGuards(JwtGuard, PermissionsGuard)
@ApiBearerAuth()
@ApiTags('Users')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Put('transfer-ownership')
  @ApiOperation({ summary: 'Transfer ownership of a user' })
  @ApiQuery({
    name: 'newOwnerId',
    required: true,
    type: String,
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({ status: 200, description: 'Returns the new owner user' })
  transferOwnership(
    @CurrentUser() currentUser: User,
    @Query('newOwnerId') newOwnerId: string,
  ): Promise<User> {
    return this.usersService.transferOwnership(currentUser, newOwnerId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all users' })
  @ApiResponse({ status: 200, description: 'Returns all users' })
  @ApiQuery({
    name: 'q',
    required: false,
    type: String,
    example: 'name:like:test',
  })
  @ApiQuery({
    name: 'fq',
    required: false,
    type: String,
    example: 'isActive:eq:true',
  })
  @ApiQuery({
    name: 'sort',
    required: false,
    type: String,
    example: 'firstName:asc;lastName:desc',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
  async findAll(
    @CurrentUser() currentUser: User,
    @Query('q') q?: string,
    @Query('fq') fq?: string,
    @Query('sort') sort?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ): Promise<{ data: User[]; count: number; page: number; limit: number }> {
    // Add filter to exclude current user'
    const userId = (currentUser as any).user;
    const users = await this.usersService.findAll({
      q,
      fq,
      sort,
      page,
      limit,
      relations: ['roles'],
    });

    return {
      data: users?.data?.filter(user => user.id !== userId) ?? [],
      count: users.count,
      page: users.page,
      limit: users.limit,
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a user by ID' })
  @ApiResponse({ status: 200, description: 'Returns the user' })
  findOne(@Param('id') id: string): Promise<User> {
    return this.usersService.findByIdOrThrow(id, ['roles']);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({ status: 201, description: 'Returns the created user' })
  create(@Body() userData: CreateUserDto): Promise<User> {
    return this.usersService.create(userData);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a user by ID' })
  @ApiResponse({ status: 200, description: 'Returns the updated user' })
  patch(
    @Param('id') id: string,
    @Body() userData: UpdateUserDto,
  ): Promise<User> {
    return this.usersService.update(id, userData);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a user by ID' })
  @ApiResponse({ status: 200, description: 'Returns the updated user' })
  put(@Param('id') id: string, @Body() userData: UpdateUserDto): Promise<User> {
    return this.usersService.update(id, userData);
  }

  // @Delete(':id')
  // @ApiOperation({ summary: 'Delete a user by ID' })
  // @ApiResponse({ status: 200, description: 'Returns the deleted user' })
  // remove(@Param('id') id: string): Promise<void> {
  //   return this.usersService.deleteById(id);
  // }
}
