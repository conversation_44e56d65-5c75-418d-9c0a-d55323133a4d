import {
  IsOptional,
  IsString,
  IsNumber,
  IsBoolean,
  IsEnum,
  IsArray,
  IsUUID,
  ValidateNested,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  <PERSON>roppingMethod,
  CropType,
  ExceptionHandlingRule,
  FileUploadFormat,
  ImageInheritRule,
  RoutingMethod,
  ShippingMethod,
  VendorAssignmentRule,
  WorkflowCategory,
} from '../enums/product-sku.enums';

class UpdateProductInfoDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  category?: string;

  @IsOptional()
  @IsString()
  description?: string;
}

class UpdateProductWithIdDto extends UpdateProductInfoDto {
  @IsUUID()
  id: string;
}

class UpdateVendorSupportedSkuDto {
  @IsOptional()
  @IsNumber()
  productionTimeDays?: number;

  @IsOptional()
  @IsNumber()
  maxCapacityPerDay?: number;
}

class UpdateVendorWithIdDto {
  @IsOptional()
  @IsString()
  vendorSku?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateVendorSupportedSkuDto)
  supportedSku?: UpdateVendorSupportedSkuDto;
}

class ShopifyVariantDto {
  @IsString()
  name: string;

  @IsString()
  value: string;
}

export class UpdateProductSkuDto {
  @IsOptional()
  @IsString()
  sku?: string;

  @IsOptional()
  @IsEnum(WorkflowCategory, {
    message: `workflowCategory must be one of: ${Object.values(WorkflowCategory).join(', ')}`
  })
  workflowCategory?: WorkflowCategory;

  @IsOptional()
  @IsBoolean()
  hasRush?: boolean;

  @IsOptional()
  @IsNumber()
  rushDays?: number;

  @IsOptional()
  @IsString()
  imageNamingConvention?: string;

  @IsOptional()
  @IsNumber()
  imageInheritancePriority?: number;

  @IsOptional()
  @IsString()
  shipStationStore?: string;

  @IsOptional()
  @IsNumber()
  shippingWeight?: number;

  @IsOptional()
  @IsNumber()
  productLength?: number;

  @IsOptional()
  @IsNumber()
  productWidth?: number;

  @IsOptional()
  @IsNumber()
  productHeight?: number;

  @IsOptional()
  @IsString()
  chinaWOFEPrice?: number;

  @IsOptional()
  @IsBoolean()
  canUpSold?: boolean;

  @IsOptional()
  @IsBoolean()
  canCrossSold?: boolean;

  @IsOptional()
  @IsBoolean()
  requireImageUpload?: boolean;

  @IsOptional()
  @IsBoolean()
  requireCropping?: boolean;

  @IsOptional()
  @IsEnum(CroppingMethod)
  croppingMethod?: CroppingMethod;

  @IsOptional()
  @IsEnum(CropType)
  cropType?: CropType;

  @IsOptional()
  @IsBoolean()
  croppingReviewRequired?: boolean;

  @IsOptional()
  @IsBoolean()
  artworkRequired?: boolean;

  @IsOptional()
  @IsBoolean()
  requireCustomerArtworkApproval?: boolean;

  @IsOptional()
  @IsBoolean()
  requireTemplate?: boolean;

  @IsOptional()
  @IsEnum(FileUploadFormat)
  fileUploadFormat?: FileUploadFormat;

  @IsOptional()
  @IsBoolean()
  canInheritImage?: boolean;

  @IsOptional()
  @IsEnum(ImageInheritRule)
  imageInheritRule?: ImageInheritRule;

  @IsOptional()
  @IsBoolean()
  canManualOverride?: boolean;

  @IsOptional()
  @IsEnum(ExceptionHandlingRule)
  exceptionHandlingRule?: ExceptionHandlingRule;

  @IsOptional()
  @IsBoolean()
  customerFollowupEnabled?: boolean;

  @IsOptional()
  @IsBoolean()
  requirePreprocessing?: boolean;

  @IsOptional()
  @IsEnum(ShippingMethod)
  shippingMethod?: ShippingMethod;

  @IsOptional()
  @IsBoolean()
  hasWorkPaper?: boolean;

  @IsOptional()
  @IsEnum(RoutingMethod)
  routingMethod?: RoutingMethod;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsUUID()
  artworkTypeId?: string;

  @IsOptional()
  @IsUUID()
  primaryVendorId?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateVendorWithIdDto)
  primaryVendorToUpdate?: UpdateVendorWithIdDto;

  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true })
  eligibleVendors?: string[];

  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true })
  removeEligibleVendors?: string[];

  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true })
  parentSkuIds?: string[];

  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true })
  childSkuIds?: string[];

  @IsOptional()
  @IsArray()
  @IsUUID('all', { each: true })
  productIds?: string[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateProductWithIdDto)
  productsToUpdate?: UpdateProductWithIdDto[];

  @IsOptional()
  @IsNumber()
  processingPriority?: number;

  @IsOptional()
  @IsEnum(VendorAssignmentRule)
  vendorAssignmentRule?: VendorAssignmentRule;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ShopifyVariantDto)
  shopifyNativeVariant?: ShopifyVariantDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ShopifyVariantDto)
  shopifyCustomVariant?: ShopifyVariantDto[];
}
