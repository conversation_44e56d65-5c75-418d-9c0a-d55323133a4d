import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  Put,
} from '@nestjs/common';
import { RolesService } from './roles.service';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtGuard } from 'src/auth/guards/jwt-auth.guard';

@UseGuards(JwtGuard)
@ApiBearerAuth()
@ApiTags('Roles')
@Controller('roles')
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  @Get('permissions')
  @ApiOperation({ summary: 'Get all permissions' })
  @ApiResponse({ status: 200, description: 'Returns all permissions' })
  getPermissions() {
    return this.rolesService.getPermissions();
  }

  @Post()
  @ApiOperation({ summary: 'Create a new role' })
  @ApiResponse({ status: 201, description: 'Returns the created role' })
  create(@Body() createRoleDto: CreateRoleDto) {
    return this.rolesService.createRole(createRoleDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all roles' })
  @ApiResponse({ status: 200, description: 'Returns all roles' })
  @ApiQuery({
    name: 'q',
    required: false,
    type: String,
    example: 'firstName:like:test',
  })
  @ApiQuery({
    name: 'fq',
    required: false,
    type: String,
    example: 'isActive:eq:true',
  })
  @ApiQuery({
    name: 'sort',
    required: false,
    type: String,
    example: 'name:asc;firstName:desc',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
  async findAll(
    @Query('q') q?: string,
    @Query('fq') fq?: string,
    @Query('sort') sort?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.rolesService.findAll({
      q,
      fq,
      sort,
      page,
      limit,
      relations: ['users'],
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a role by ID' })
  @ApiResponse({ status: 200, description: 'Returns the role' })
  findOne(@Param('id') id: string) {
    return this.rolesService.findByIdOrThrow(id, ['users']);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a role by ID' })
  @ApiResponse({ status: 200, description: 'Returns the updated role' })
  patch(@Param('id') id: string, @Body() updateRole: UpdateRoleDto) {
    return this.rolesService.updateRole(id, updateRole);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a role by ID' })
  @ApiResponse({ status: 200, description: 'Returns the updated role' })
  put(@Param('id') id: string, @Body() updateRole: UpdateRoleDto) {
    return this.rolesService.updateRole(id, updateRole);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a role by ID' })
  @ApiResponse({ status: 200, description: 'Returns the deleted role' })
  remove(@Param('id') id: string) {
    return this.rolesService.deleteById(id);
  }
}
